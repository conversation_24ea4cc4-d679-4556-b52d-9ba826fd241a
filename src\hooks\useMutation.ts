import { useMutation, UseMutationOptions, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import {
  activateCV,
  applyForJob,
  changePassword,
  createConversation,
  createJobPosting,
  deactivateAccount,
  deleteApplication,
  deleteConversation,
  deleteCV,
  deleteJobById,
  // deleteMessage, // Removed as we're now using socket events
  deletePhoto,
  deletePortfolioImage,
  deleteVideo,
  forgetPassword,
  loginUser,
  logoutUser,
  rejectApplicant,
  registerCandidate,
  registerRecruiter,
  resetPassword,
  saveCandidate,
  saveJob,
  sendMessage,
  shortlistApplicant,
  submitContactForm,
  unsaveCandidate,
  unsaveJob,
  updateCompanyProfile,
  updateJob,
  updateJobSeekerProfile,
  updateJobStatus,
  uploadMediaForChat,
  extractCVDetails,
  boostJob,
  purchasePro,
  notificationReadById, // Added for notification read service
  markAllNotificationsAsRead, // Added for marking all notifications as read service
  addFirebaseToken,
  removeFirebaseToken,
} from "@/service/mutation.service";
import { ApiError } from "@/types/common.types";
import {
  IActivateCVRequestDto,
  IActivateCVResponseDto,
  IApplyJobResponseDto,
  ICandidateRegisterRequestDto,
  IChangePasswordRequestDto,
  IChangePasswordResponseDto,
  IContactFormRequestDto,
  IContactFormResponseDto,
  ICreateConversationRequestDto,
  ICreateConversationResponseDto,
  IDeactivateAccoutResponseDto,
  IDeleteApplicationResponseDto,
  IDeleteConversationResponseDto,
  IDeleteMessageRequestDto,
  // IDeleteMessageResponseDto, // Removed as we're now using socket events
  IDeletePortfolioImageResponseDto,
  IForgetPasswordRequestDto,
  IForgetPasswordResponseDto,
  ILoginRequestDto,
  IRecruiterRegisterRequestDto,
  IRejectApplicantResponseDto,
  IResetPasswordRequestDto,
  IResetPasswordResponseDto,
  ISaveCandidateResponseDto,
  ISaveJobResponseDto,
  ISendMessageRequestDto,
  ISendMessageResponseDto,
  IShortlistApplicantResponseDto,
  ISignInResponseDto,
  ISignUpResponseDto,
  IUnsaveCandidateResponseDto,
  IUnsaveJobResponseDto,
  IUpdateCompanyProfileRequestDto,
  IUpdateCompanyProfileResponseDto,
  IUpdateJobSeekerProfileRequestDto,
  IUpdateJobSeekerProfileResponseDto,
  IUpdateJobStatusResponseDto,
  IUploadMediaResponseDto,
  IOCRExtractRequestDto,
  IOCRExtractResponseDto,
  IBoostJobRequestDto,
  IBoostJobResponseDto,
  IPurchaseProRequestDto,
  IPurchaseProResponseDto,
  IMarkNotificationAsReadResponseDto, // Added for notification read service
  IMarkAllNotificationsAsReadResponseDto, // Added for marking all notifications as read service
  IFirebaseTokenRequestDto,
  IFirebaseTokenResponseDto,
} from "@/types/mutation.types";

import {
  ICreateJobRequestDto,
  ICreateJobResponseDto,
  ICvAttachment,
  IMessage,
} from "@/types/query.types";
import { uploadFile, uploadMultipleFiles } from "@/utils/upload";

export function useLogin(
  options?: Omit<UseMutationOptions<ISignInResponseDto, ApiError, ILoginRequestDto>, "mutationFn">
) {
  return useMutation<ISignInResponseDto, ApiError, ILoginRequestDto>({
    mutationFn: loginUser,

    ...options,
  });
}

export function useAddFirebaseToken(
  options?: Omit<
    UseMutationOptions<IFirebaseTokenResponseDto, ApiError, IFirebaseTokenRequestDto>,
    "mutationFn"
  >
) {
  const queryClient = useQueryClient();

  return useMutation<IFirebaseTokenResponseDto, ApiError, IFirebaseTokenRequestDto>({
    mutationFn: addFirebaseToken,
    onSuccess: (data) => {
      toast.success(data.message || "Firebase token added successfully");
      queryClient.invalidateQueries({ queryKey: ["get-firebase-tokens"] });
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to add Firebase token");
      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.error("Add Firebase token error:", error);
      }
    },
    ...options,
  });
}

export function useRemoveFirebaseToken(
  options?: Omit<
    UseMutationOptions<IFirebaseTokenResponseDto, ApiError, IFirebaseTokenRequestDto>,
    "mutationFn"
  >
) {
  const queryClient = useQueryClient();

  return useMutation<IFirebaseTokenResponseDto, ApiError, IFirebaseTokenRequestDto>({
    mutationFn: removeFirebaseToken,
    onSuccess: (data) => {
      toast.success(data.message || "Firebase token removed successfully");
      queryClient.invalidateQueries({ queryKey: ["get-firebase-tokens"] });
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to remove Firebase token");
      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.error("Remove Firebase token error:", error);
      }
    },
    ...options,
  });
}

export function useNotificationReadById(
  options?: Omit<
    UseMutationOptions<IMarkNotificationAsReadResponseDto, ApiError, string>,
    "mutationFn"
  >
) {
  const queryClient = useQueryClient();

  return useMutation<IMarkNotificationAsReadResponseDto, ApiError, string>({
    mutationFn: notificationReadById,
    onSuccess: (data) => {
      toast.success(data.message || "Notification marked as read");
      queryClient.invalidateQueries({ queryKey: ["get-notifications"] });
      queryClient.invalidateQueries({ queryKey: ["get-unread-notifications-count"] });
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to mark notification as read");
      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.error("Mark notification as read error:", error);
      }
    },
    ...options,
  });
}

export function useMarkAllNotificationsAsRead(
  options?: Omit<
    UseMutationOptions<IMarkAllNotificationsAsReadResponseDto, ApiError, void>,
    "mutationFn"
  >
) {
  const queryClient = useQueryClient();

  return useMutation<IMarkAllNotificationsAsReadResponseDto, ApiError, void>({
    mutationFn: markAllNotificationsAsRead,
    onSuccess: (data) => {
      toast.success(data.message || "All notifications marked as read");
      queryClient.invalidateQueries({ queryKey: ["get-notifications"] });
      queryClient.invalidateQueries({ queryKey: ["get-unread-notifications-count"] });
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to mark all notifications as read");
      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.error("Mark all notifications as read error:", error);
      }
    },
    ...options,
  });
}

export function useLogout(options?: Omit<UseMutationOptions<void, ApiError, void>, "mutationFn">) {
  return useMutation<void, ApiError, void>({
    mutationFn: logoutUser,

    ...options,
  });
}

export function useRegisterCandidate(
  options?: Omit<
    UseMutationOptions<ISignUpResponseDto, ApiError, ICandidateRegisterRequestDto>,
    "mutationFn"
  >
) {
  return useMutation<ISignUpResponseDto, ApiError, ICandidateRegisterRequestDto>({
    mutationFn: registerCandidate,

    ...options,
  });
}

export function useRegisterRecruiter(
  options?: Omit<
    UseMutationOptions<ISignUpResponseDto, ApiError, IRecruiterRegisterRequestDto>,
    "mutationFn"
  >
) {
  return useMutation<ISignUpResponseDto, ApiError, IRecruiterRegisterRequestDto>({
    mutationFn: registerRecruiter,
    ...options,
  });
}

export function useForgetPassword(
  options?: Omit<
    UseMutationOptions<IForgetPasswordResponseDto, ApiError, IForgetPasswordRequestDto>,
    "mutationFn"
  >
) {
  return useMutation<IForgetPasswordResponseDto, ApiError, IForgetPasswordRequestDto>({
    mutationFn: forgetPassword,

    ...options,
  });
}

export function useResetPassword(
  options?: Omit<
    UseMutationOptions<IResetPasswordResponseDto, ApiError, IResetPasswordRequestDto>,
    "mutationFn"
  >
) {
  return useMutation<IResetPasswordResponseDto, ApiError, IResetPasswordRequestDto>({
    mutationFn: resetPassword,

    ...options,
  });
}

export function useUpdateJobSeekerProfile(
  options?: Omit<
    UseMutationOptions<
      IUpdateJobSeekerProfileResponseDto,
      ApiError,
      Partial<IUpdateJobSeekerProfileRequestDto>
    >,
    "mutationFn"
  >
) {
  return useMutation<
    IUpdateJobSeekerProfileResponseDto,
    ApiError,
    Partial<IUpdateJobSeekerProfileRequestDto>
  >({
    mutationFn: updateJobSeekerProfile,
    ...options,
  });
}

export function useUpdateCompanyProfile(
  options?: Omit<
    UseMutationOptions<
      IUpdateCompanyProfileResponseDto,
      ApiError,
      Partial<IUpdateCompanyProfileRequestDto>
    >,
    "mutationFn"
  >
) {
  return useMutation<
    IUpdateCompanyProfileResponseDto,
    ApiError,
    Partial<IUpdateCompanyProfileRequestDto>
  >({
    mutationFn: updateCompanyProfile,
    ...options,
  });
}

interface BaseResponseStructure {
  success: boolean;
  message: string;
}
interface UploadResponse extends BaseResponseStructure {
  data: {
    profilePicture: string;
  };
}

interface UploadVideoResponse extends BaseResponseStructure {
  data: {
    companyVideo: {
      url: string;
      s3Key: string;
    };
  };
}
interface MultipleUploadResponse {
  urls: string[];
  s3Keys: string[];
}

export const useUploadProfilePicture = () => {
  const queryClient = useQueryClient();

  return useMutation<UploadResponse, Error, File>({
    mutationFn: async (file: File) => {
      return uploadFile(file, "/upload/profile", "avatar");
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["get-company-profile"] });
      queryClient.invalidateQueries({ queryKey: ["get-job-seeker-profile"] });
    },
  });
};

export const useUploadCompanyVideo = () => {
  // const queryClient = useQueryClient();

  return useMutation<UploadVideoResponse, Error, File>({
    mutationFn: async (file: File) => {
      return uploadFile(file, "/upload/company/video", "video");
    },
    onSuccess: () => {
      // queryClient.invalidateQueries({ queryKey: ["get-company-profile"] });
    },
  });
};

export const useUploadCompanyPhotos = (
  options?: UseMutationOptions<MultipleUploadResponse, Error, File[]>
) => {
  return useMutation<MultipleUploadResponse, Error, File[]>({
    mutationFn: async (files: File[]) => {
      return uploadMultipleFiles(files, "/upload/company/photo", "photo");
    },
    ...options,
  });
};

type UploadPortfolioVariables = {
  files: File[];
  options?: {
    onProgress?: (progress: number) => void;
  };
};

export const useUploadPortfolio = () => {
  const queryClient = useQueryClient();

  return useMutation<MultipleUploadResponse, Error, UploadPortfolioVariables>({
    mutationFn: async ({ files, options }) => {
      return uploadMultipleFiles(files, "/upload/portfolio", "portfolio", options?.onProgress);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["get-job-seeker-profile"] });
    },
  });
};

type UploadCVVariables = {
  file: File;
  options?: {
    onProgress?: (progress: number) => void;
  };
};

export const useUploadCV = () => {
  const queryClient = useQueryClient();

  return useMutation<IOCRExtractRequestDto, Error, UploadCVVariables>({
    mutationFn: async ({ file, options }) => {
      return uploadFile(file, "/upload/cv", "cv", options?.onProgress);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["get-job-seeker-profile"] });
    },
  });
};

export const useDeleteCV = (
  options?: Omit<
    UseMutationOptions<
      { success: boolean; message: string; data: Record<string, ICvAttachment> },
      ApiError,
      { s3Key: string }
    >,
    "mutationFn"
  >
) => {
  const queryClient = useQueryClient();

  return useMutation<
    { success: boolean; message: string; data: Record<string, ICvAttachment> },
    ApiError,
    { s3Key: string }
  >({
    mutationFn: deleteCV,
    onSuccess: (data) => {
      toast.success(data.message || "CV deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["get-jobseeker-profile"] });
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to delete CV");
      // Log error in development only
      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.error("Delete CV error:", error);
      }
    },
    ...options,
  });
};

export const useDeleteVideo = (
  options?: Omit<
    UseMutationOptions<
      { success: boolean; message: string; data: Record<string, unknown> },
      ApiError,
      { s3Key: string }
    >,
    "mutationFn"
  >
) => {
  const queryClient = useQueryClient();

  return useMutation<
    { success: boolean; message: string; data: Record<string, unknown> },
    ApiError,
    { s3Key: string }
  >({
    mutationFn: deleteVideo,
    onSuccess: (data) => {
      toast.success(data.message || "Video deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["get-company-profile"] });
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to delete video");
      // Log error in development only
      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.error("Delete video error:", error);
      }
    },
    ...options,
  });
};

export const useDeletePortfolioImage = (
  options?: Omit<
    UseMutationOptions<IDeletePortfolioImageResponseDto, ApiError, { s3Key: string }>,
    "mutationFn"
  >
) => {
  const queryClient = useQueryClient();

  return useMutation<IDeletePortfolioImageResponseDto, ApiError, { s3Key: string }>({
    mutationFn: deletePortfolioImage,
    onSuccess: (data) => {
      toast.success(data.message || "Portfolio image deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["get-jobseeker-profile"] }); // Refresh jobseeker profile data
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to delete portfolio image");
      // Log error in development only
      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.error("Delete Portfolio Image error:", error);
      }
    },
    ...options,
  });
};

export const useDeletePhoto = (
  options?: Omit<
    UseMutationOptions<{ success: boolean; message: string }, ApiError, { s3Key: string }>,
    "mutationFn"
  >
) => {
  const queryClient = useQueryClient();

  return useMutation<{ success: boolean; message: string }, ApiError, { s3Key: string }>({
    mutationFn: deletePhoto, // Use the deletePhoto function from mutation.service.ts
    onSuccess: (data) => {
      toast.success(data.message || "Photo deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["get-company-profile"] }); // Refresh company profile data
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to delete photo");
      // Log error in development only
      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.error("Delete Photo error:", error);
      }
    },
    ...options,
  });
};

export function useChangePassword(
  options?: Omit<
    UseMutationOptions<IChangePasswordResponseDto, ApiError, IChangePasswordRequestDto>,
    "mutationFn"
  >
) {
  return useMutation<IChangePasswordResponseDto, ApiError, IChangePasswordRequestDto>({
    mutationFn: changePassword,
    ...options,
  });
}

export function useDeactivateAccount(
  options?: Omit<
    UseMutationOptions<IDeactivateAccoutResponseDto, ApiError, IDeactivateAccoutResponseDto>,
    "mutationFn"
  >
) {
  return useMutation<IDeactivateAccoutResponseDto, ApiError, IDeactivateAccoutResponseDto>({
    mutationFn: deactivateAccount,
    ...options,
  });
}

export function useJobPosting(
  options?: UseMutationOptions<ICreateJobResponseDto, ApiError, ICreateJobRequestDto>
) {
  return useMutation<ICreateJobResponseDto, ApiError, ICreateJobRequestDto>({
    mutationFn: createJobPosting, // Mutation function for creating a job posting
    ...options,
  });
}

export function useUpdateJob(
  options?: UseMutationOptions<
    ICreateJobResponseDto,
    ApiError,
    { id: string; data: ICreateJobRequestDto }
  >
) {
  return useMutation<ICreateJobResponseDto, ApiError, { id: string; data: ICreateJobRequestDto }>({
    mutationFn: ({ id, data }) => updateJob(id, data), // Call the updateJob service
    ...options,
  });
}

export function useDeleteJob(
  options?: Omit<
    UseMutationOptions<{ success: boolean; message: string }, ApiError, string>,
    "mutationFn"
  >
) {
  const queryClient = useQueryClient();

  return useMutation<{ success: boolean; message: string }, ApiError, string>({
    mutationFn: deleteJobById,
    onSuccess: (data) => {
      toast.success(data.message || "Job deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["get-recruiter-jobs"] }); // Refresh the job list
      queryClient.invalidateQueries({ queryKey: ["get-recent-jobs"] }); // Refresh the job list
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to delete job");
    },
    ...options,
  });
}

export function useUpdateJobStatus(
  options?: Omit<
    UseMutationOptions<
      IUpdateJobStatusResponseDto,
      ApiError,
      { jobId: string; isJobActive: boolean }
    >,
    "mutationFn"
  >
) {
  const queryClient = useQueryClient();

  return useMutation<
    IUpdateJobStatusResponseDto,
    ApiError,
    { jobId: string; isJobActive: boolean }
  >({
    mutationFn: ({ jobId, isJobActive }) => updateJobStatus(jobId, isJobActive), // Call the service with jobId and isJobActive
    onSuccess: (data, variables) => {
      toast.success(data.message || "Job status updated successfully");
      queryClient.invalidateQueries({ queryKey: ["get-job-by-id", variables.jobId] }); // Refresh the job list
      queryClient.invalidateQueries({ queryKey: ["get-recruiter-jobs"] }); // Refresh the job list
      queryClient.invalidateQueries({ queryKey: ["get-recent-jobs"] }); // Refresh the job list
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to update job status");
    },
    ...options,
  });
}

export function useContactForm(
  options?: Omit<
    UseMutationOptions<IContactFormResponseDto, ApiError, IContactFormRequestDto>,
    "mutationFn"
  >
) {
  return useMutation<IContactFormResponseDto, ApiError, IContactFormRequestDto>({
    mutationFn: submitContactForm,
    onSuccess: (data) => {
      toast.success(data.message || "Message sent successfully");
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to send message");
      // Log error in development only
      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.error("Contact form error:", error);
      }
    },
    ...options,
  });
}

export function useCreateConversation(
  options?: Omit<
    UseMutationOptions<ICreateConversationResponseDto, ApiError, ICreateConversationRequestDto>,
    "mutationFn"
  >
) {
  const queryClient = useQueryClient();

  return useMutation<ICreateConversationResponseDto, ApiError, ICreateConversationRequestDto>({
    mutationFn: (data: ICreateConversationRequestDto) => createConversation(data),
    onSuccess: (data) => {
      toast.success(data.message || "Conversation created successfully");
      queryClient.invalidateQueries({ queryKey: ["get-conversations"] });

      // After successful conversation creation, join the conversation via socket
      // This will trigger the join_conversation event
      if (data.data && data.data._id) {
        import("@/service/socket.service").then(({ joinConversation }) => {
          import("@/lib/debug").then(({ debugLog, debugError }) => {
            // Log the conversation type for debugging
            const conversationType = data.data.isDirectMessage ? "direct" : "application";
            debugLog(`Created conversation of type: ${conversationType} with ID: ${data.data._id}`);

            // Join the conversation via socket
            joinConversation(data.data._id)
              .then(() => {
                debugLog(`Joined conversation: ${data.data._id}`);

                // Listen for the create_conversation event
                // This is handled automatically by the SocketProvider
                debugLog(
                  `Waiting for create_conversation event for conversation: ${data.data._id}`
                );
              })
              .catch((error) => {
                debugError(`Failed to join conversation: ${data.data._id}`, error);
              });
          });
        });
      }
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to create conversation");
      // Log error in development only
      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.error("Create conversation error:", error);
      }
    },
    ...options,
  });
}

export function useDeleteConversation(
  options?: Omit<UseMutationOptions<IDeleteConversationResponseDto, ApiError, string>, "mutationFn">
) {
  const queryClient = useQueryClient();

  return useMutation<IDeleteConversationResponseDto, ApiError, string>({
    // Step 1: Call the API endpoint to delete the conversation
    // This will update the deletedBy array on the server
    mutationFn: (conversationId: string) => deleteConversation(conversationId),

    // Step 2: On success, emit the socket event and refresh the conversation list
    onSuccess: async (data, conversationId) => {
      // Import the debug utilities
      const { debugLog, debugError } = await import("@/lib/debug");

      // Log the successful API call
      debugLog(`API: Conversation ${conversationId} deleted successfully`);
      debugLog(`API: Server has added current user to the deletedBy array`);

      // Show success message to the user
      toast.success(data.message || "Conversation deleted successfully");

      try {
        // Import the socket service dynamically to avoid circular dependencies
        const { deleteConversationSocket } = await import("@/service/socket.service");

        // Step 3: Emit the socket event to notify other clients
        debugLog(`Socket: Emitting delete_conversation event for conversation ${conversationId}`);
        const socketSuccess = await deleteConversationSocket(conversationId);

        if (socketSuccess) {
          debugLog(`Socket: Event delete_conversation emitted successfully`);
        } else {
          debugError(`Socket: Failed to emit delete_conversation event`);
        }
      } catch (error) {
        // If socket event fails, it's not critical as the API call succeeded
        debugError(`Socket: Error emitting delete_conversation event:`, error);
      } finally {
        // Step 4: Refresh the conversation list regardless of socket event success
        debugLog(`Cache: Invalidating conversations query to refresh the list`);
        debugLog(`Cache: This will fetch the updated list with deletedBy array`);
        debugLog(`UI: Conversations with current user in deletedBy will be filtered out`);

        // Force immediate refetch to ensure we get the latest data with updated deletedBy arrays
        debugLog(`Cache: Refetching conversations list to update UI immediately`);
        queryClient.refetchQueries({ queryKey: ["get-conversations"] });

        // Also refetch the specific conversation queries to ensure the UI is updated
        debugLog(`Cache: Refetching specific conversation queries for ID: ${conversationId}`);
        queryClient.refetchQueries({ queryKey: ["get-messages", conversationId] });
        queryClient.refetchQueries({ queryKey: ["get-messages-infinite", conversationId] });

        // Remove the conversation data from the cache completely to ensure it's refetched
        debugLog(`Cache: Removing conversation data from cache for ID: ${conversationId}`);
        queryClient.removeQueries({ queryKey: ["get-messages", conversationId] });
        queryClient.removeQueries({ queryKey: ["get-messages-infinite", conversationId] });

        // Call the original onSuccess handler if provided
        if (options?.onSuccess) {
          // We need to cast to any here because the third parameter is expected to be a context object
          // but we don't have one in this case
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          options.onSuccess(data, conversationId, undefined as any);
        }
      }
    },

    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to delete conversation");
      // Log error in development only
      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.error("Delete conversation error:", error);
      }

      // Call the original onError handler if provided
      if (options?.onError) {
        options.onError(error, "", undefined);
      }
    },
  });
}

export function useSendMessage(
  options?: Omit<
    UseMutationOptions<ISendMessageResponseDto, ApiError, ISendMessageRequestDto>,
    "mutationFn"
  >
) {
  // We don't need queryClient here as we're using socket events to update the UI
  // const queryClient = useQueryClient();

  return useMutation<ISendMessageResponseDto, ApiError, ISendMessageRequestDto>({
    mutationFn: (data: ISendMessageRequestDto) => sendMessage(data),
    onSuccess: (_) => {
      // Don't show toast for every message
      // toast.success("Message sent successfully");
      // We don't need to invalidate queries here because the socket event will do that
      // This prevents duplicate messages from appearing
      // The socket event handler will remove the pending message and update the queries
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to send message");
      // Log error in development only
      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.error("Send message error:", error);
      }
    },
    ...options,
  });
}

export function useDeleteMessage(
  options?: Omit<UseMutationOptions<boolean, Error, IDeleteMessageRequestDto>, "mutationFn">
) {
  const queryClient = useQueryClient();

  return useMutation<boolean, Error, IDeleteMessageRequestDto>({
    // Use socket event to delete the message instead of API call
    mutationFn: async (data: IDeleteMessageRequestDto) => {
      // Import the debug utilities and socket service
      const { debugLog } = await import("@/lib/debug");
      const { deleteMessageSocket, listenForMessageDeleted } = await import(
        "@/service/socket.service"
      );

      debugLog(`Socket: Deleting message ${data.messageId} via socket event`);

      // Set up a listener for the message_deleted event before sending the delete request
      // This ensures we don't miss the event if it comes back quickly
      return new Promise<boolean>((resolve, reject) => {
        try {
          // Set up a listener for the message_deleted event
          const cleanupListener = listenForMessageDeleted(async (socketData) => {
            // Check if this is the message we're trying to delete
            if (socketData.messageId === data.messageId) {
              debugLog(`Received message_deleted event for message ${data.messageId}`);

              // Get the chat store methods
              const chatStore = await import("@/store/useChatStore");
              const { messages, lastMessages, updateMessage, updateLastMessage } =
                chatStore.useChatStore.getState();

              // First, find the message in the store
              const conversationId = data.conversationId || "";
              const conversationMessages = messages[conversationId] || [];
              const messageIndex = conversationMessages.findIndex(
                (msg: IMessage) => msg._id === data.messageId
              );

              if (messageIndex !== -1) {
                const message = conversationMessages[messageIndex];

                if (message) {
                  // Update the message based on deletion type
                  if (socketData.deleteForEveryone) {
                    // For deleteForEveryone, update the message content
                    updateMessage(conversationId, data.messageId, {
                      content: "This message has been deleted",
                      deletedForEveryone: true,
                      updatedAt: new Date().toISOString(),
                    });
                  } else if (socketData.userId || socketData.deletedBy) {
                    // For deleteForMe, update the deletedBy array
                    const deletedBy = [...(message.deletedBy || [])];

                    // Handle both userId and deletedBy fields
                    if (socketData.userId && !deletedBy.includes(socketData.userId)) {
                      deletedBy.push(socketData.userId);
                    }

                    if (socketData.deletedBy && !deletedBy.includes(socketData.deletedBy)) {
                      deletedBy.push(socketData.deletedBy);
                    }

                    updateMessage(conversationId, data.messageId, {
                      deletedBy,
                    });
                  }

                  // Check if this is the last message in the conversation
                  const lastMessage = lastMessages[conversationId];
                  if (lastMessage && lastMessage._id === data.messageId) {
                    // Update the last message in the conversation
                    const updatedMessage = conversationMessages[messageIndex];
                    if (updatedMessage) {
                      updateLastMessage(conversationId, updatedMessage);
                    }
                  }
                }
              }

              // Update the cache directly for immediate UI update without triggering API calls
              const updateMessageInCache = (oldData: unknown) => {
                if (!oldData) return oldData;

                // For regular query data structure
                if (oldData && typeof oldData === "object" && "data" in oldData) {
                  const typedData = oldData as { data: { messages: IMessage[] } };

                  // Update messages based on deletion type
                  const updatedMessages = typedData.data.messages.map((msg) => {
                    if (msg._id !== socketData.messageId) return msg;

                    // Handle deleteForEveryone case
                    if (socketData.deleteForEveryone) {
                      return {
                        ...msg,
                        content: "This message has been deleted",
                        deletedForEveryone: true,
                        updatedAt: new Date().toISOString(),
                      };
                    }
                    // Handle deleteForMe case
                    else if (socketData.userId || socketData.deletedBy) {
                      const deletedBy = [...(msg.deletedBy || [])];

                      // Handle both userId and deletedBy fields
                      if (socketData.userId && !deletedBy.includes(socketData.userId)) {
                        deletedBy.push(socketData.userId);
                      }

                      if (socketData.deletedBy && !deletedBy.includes(socketData.deletedBy)) {
                        deletedBy.push(socketData.deletedBy);
                      }

                      return {
                        ...msg,
                        deletedBy,
                      };
                    }

                    return msg;
                  });

                  return {
                    ...oldData,
                    data: {
                      ...typedData.data,
                      messages: updatedMessages,
                    },
                  };
                }

                // For infinite query data structure
                if (
                  oldData &&
                  typeof oldData === "object" &&
                  "pages" in oldData &&
                  Array.isArray(oldData.pages)
                ) {
                  return {
                    ...oldData,
                    pages: oldData.pages.map((page) => {
                      if (typeof page === "object" && page !== null && "data" in page) {
                        const typedPage = page as { data: { messages: IMessage[] } };

                        // Update messages based on deletion type
                        const updatedMessages = typedPage.data.messages.map((msg) => {
                          if (msg._id !== socketData.messageId) return msg;

                          // Handle deleteForEveryone case
                          if (socketData.deleteForEveryone) {
                            return {
                              ...msg,
                              content: "This message has been deleted",
                              deletedForEveryone: true,
                              updatedAt: new Date().toISOString(),
                            };
                          }
                          // Handle deleteForMe case
                          else if (socketData.userId || socketData.deletedBy) {
                            const deletedBy = [...(msg.deletedBy || [])];

                            // Handle both userId and deletedBy fields
                            if (socketData.userId && !deletedBy.includes(socketData.userId)) {
                              deletedBy.push(socketData.userId);
                            }

                            if (socketData.deletedBy && !deletedBy.includes(socketData.deletedBy)) {
                              deletedBy.push(socketData.deletedBy);
                            }

                            return {
                              ...msg,
                              deletedBy,
                            };
                          }

                          return msg;
                        });

                        return {
                          ...page,
                          data: {
                            ...typedPage.data,
                            messages: updatedMessages,
                          },
                        };
                      }
                      return page;
                    }),
                  };
                }

                return oldData;
              };

              // Update the cache directly without triggering API calls
              if (conversationId) {
                queryClient.setQueryData(["get-messages", conversationId], updateMessageInCache);
                queryClient.setQueryData(
                  ["get-messages-infinite", conversationId],
                  updateMessageInCache
                );
              }

              debugLog(`Store and cache updated for deleted message ${data.messageId}`);

              // Remove the listener since we've handled the event
              cleanupListener();

              // Resolve the promise
              resolve(true);
            }
          });

          // Set a timeout to clean up the listener if we don't receive the event
          const timeoutId = setTimeout(async () => {
            debugLog(`Timeout waiting for message_deleted event for message ${data.messageId}`);
            cleanupListener();

            // Even if we don't receive the socket event, we should still update the chat store
            // This ensures the UI is updated even if there's a network issue
            debugLog(`Manually updating store for deleted message ${data.messageId} after timeout`);

            // Get the current user ID from the store
            const { currentUser } = await import("@/store/useUserStore").then((m) =>
              m.useUserStore.getState()
            );

            // Get the chat store methods
            const chatStore = await import("@/store/useChatStore");
            const { deleteMessage } = chatStore.useChatStore.getState();

            // Option 1: Use the deleteMessage method from the store
            // This handles all the logic for updating the store
            if (data.conversationId) {
              deleteMessage(
                data.conversationId,
                data.messageId,
                data.deleteForEveryone || false,
                currentUser?._id
              );
            }

            // Option 2: Update the cache directly
            // This ensures the UI is updated even if the store update fails
            const updateMessageInCache = (oldData: unknown) => {
              if (!oldData) return oldData;

              // For regular query data structure
              if (oldData && typeof oldData === "object" && "data" in oldData) {
                const typedData = oldData as { data: { messages: IMessage[] } };

                // Update messages based on deletion type
                const updatedMessages = typedData.data.messages.map((msg) => {
                  if (msg._id !== data.messageId) return msg;

                  // Handle deleteForEveryone case
                  if (data.deleteForEveryone) {
                    return {
                      ...msg,
                      content: "This message has been deleted",
                      deletedForEveryone: true,
                      updatedAt: new Date().toISOString(),
                    };
                  }
                  // Handle deleteForMe case
                  else if (currentUser?._id) {
                    const deletedBy = [...(msg.deletedBy || [])];

                    // Add current user ID to deletedBy array
                    if (!deletedBy.includes(currentUser._id)) {
                      deletedBy.push(currentUser._id);
                    }

                    return {
                      ...msg,
                      deletedBy,
                    };
                  }

                  return msg;
                });

                return {
                  ...oldData,
                  data: {
                    ...typedData.data,
                    messages: updatedMessages,
                  },
                };
              }

              // For infinite query data structure
              if (
                oldData &&
                typeof oldData === "object" &&
                "pages" in oldData &&
                Array.isArray(oldData.pages)
              ) {
                return {
                  ...oldData,
                  pages: oldData.pages.map((page) => {
                    if (typeof page === "object" && page !== null && "data" in page) {
                      const typedPage = page as { data: { messages: IMessage[] } };

                      // Update messages based on deletion type
                      const updatedMessages = typedPage.data.messages.map((msg) => {
                        if (msg._id !== data.messageId) return msg;

                        // Handle deleteForEveryone case
                        if (data.deleteForEveryone) {
                          return {
                            ...msg,
                            content: "This message has been deleted",
                            deletedForEveryone: true,
                            updatedAt: new Date().toISOString(),
                          };
                        }
                        // Handle deleteForMe case
                        else if (currentUser?._id) {
                          const deletedBy = [...(msg.deletedBy || [])];

                          // Add current user ID to deletedBy array
                          if (!deletedBy.includes(currentUser._id)) {
                            deletedBy.push(currentUser._id);
                          }

                          return {
                            ...msg,
                            deletedBy,
                          };
                        }

                        return msg;
                      });

                      return {
                        ...page,
                        data: {
                          ...typedPage.data,
                          messages: updatedMessages,
                        },
                      };
                    }
                    return page;
                  }),
                };
              }

              return oldData;
            };

            // Update the cache directly without triggering API calls
            if (data.conversationId) {
              queryClient.setQueryData(["get-messages", data.conversationId], updateMessageInCache);
              queryClient.setQueryData(
                ["get-messages-infinite", data.conversationId],
                updateMessageInCache
              );
            }

            // Still resolve as true since we've manually updated the store
            resolve(true);
          }, 5000); // 5 second timeout

          // Now that the listener is set up, send the delete request via socket
          deleteMessageSocket(data.messageId, data.deleteForEveryone || false)
            .then((success) => {
              if (!success) {
                // If the socket request failed, clean up and reject
                clearTimeout(timeoutId);
                cleanupListener();
                reject(new Error("Failed to delete message via socket"));
              }
              // Otherwise, wait for the message_deleted event
            })
            .catch((error) => {
              // If there was an error sending the socket request, clean up and reject
              clearTimeout(timeoutId);
              cleanupListener();
              reject(error);
            });
        } catch (error) {
          // If there was an error setting up the listener, reject
          reject(error);
        }
      });
    },

    // On success, show a toast message
    onSuccess: (success, variables) => {
      // Show success message to the user
      toast.success("Message deleted successfully");

      // Call the original onSuccess handler if provided
      if (options?.onSuccess) {
        options.onSuccess(success, variables, undefined);
      }
    },

    onError: (error, variables) => {
      toast.error(error.message || "Failed to delete message");

      if (options?.onError) {
        options.onError(error, variables, undefined);
      }
    },
  });
}

// Define a type for the upload media variables
type UploadMediaVariables = {
  file: File[];
  conversationId: string;
  content: string;
  options?: {
    onProgress?: (progress: number) => void;
  };
};

export function useUploadMediaForChat(
  options?: Omit<
    UseMutationOptions<IUploadMediaResponseDto, ApiError, UploadMediaVariables>,
    "mutationFn"
  >
) {
  // We don't need queryClient here as we're not invalidating queries
  // const queryClient = useQueryClient();

  return useMutation<IUploadMediaResponseDto, ApiError, UploadMediaVariables>({
    mutationFn: async ({ file, options: uploadOptions }) => {
      // Check if files exceed the maximum count (5 files)
      const MAX_FILES = 5;
      if (file.length > MAX_FILES) {
        throw new Error(`You can only upload a maximum of ${MAX_FILES} files at once.`);
      }

      // Check if any files exceed the size limit (3MB)
      const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB in bytes
      for (const fileOne of file) {
        if (fileOne.size > MAX_FILE_SIZE) {
          throw new Error(`File "${fileOne.name}" exceeds the 5MB size limit.`);
        }
      }

      // Create FormData object for media upload
      const formData = new FormData();
      file.forEach((file) => {
        formData.append("file", file);
      });

      // First upload the media files
      const uploadResponse = await uploadMediaForChat(formData, uploadOptions?.onProgress);

      // If media upload is successful, send the message with media
      if (uploadResponse.success && uploadResponse.data) {
        // Create message data with media files
        // const messageData: ISendMessageRequestDto = {
        //   conversationId,
        //   content,
        //   mediaFiles: uploadResponse.data.mediaFiles.map((file) => ({
        //     url: file.url,
        //     s3Key: file.s3Key,
        //     fileName: file.fileName,
        //     fileSize: file.fileSize,
        //   })),
        // };

        // Send the message with media
        // const messageResponse = await sendMessage(messageData);

        // Return the combined response
        return {
          ...uploadResponse,
          // messageResponse,
        };
      }

      return uploadResponse;
    },
    onSuccess: () => {
      // Show success message
      toast.success("Message sent successfully");

      // No need to invalidate any queries here
      // The socket events will update the Zustand store which is the source of truth
      // This prevents unnecessary API calls when sending messages
    },
    onError: (error) => {
      if (!options?.onError) {
        // Only show toast if no custom error handler is provided
        toast.error(
          error.message || error.response?.data?.message || "Failed to send message with media"
        );
      }

      // Log error in development only
      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.error("Upload media and send message error:", error);
      }
    },
    ...options,
  });
}

export function useApplyJob(
  options?: Omit<UseMutationOptions<IApplyJobResponseDto, ApiError, string>, "mutationFn">
) {
  const queryClient = useQueryClient();

  return useMutation<IApplyJobResponseDto, ApiError, string>({
    mutationFn: applyForJob,
    onSuccess: (data) => {
      // Default success handler if not provided in options
      if (!options?.onSuccess) {
        toast.success(data.message || "Application submitted successfully");
      }
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ["get-all-jobs"] });
    },
    onError: (error) => {
      // Default error handler if not provided in options
      if (!options?.onError) {
        toast.error(error.response?.data?.message || "Failed to apply for this job");
      }
    },
    ...options,
  });
}

export function useSaveCandidate(
  options?: Omit<UseMutationOptions<ISaveCandidateResponseDto, ApiError, string>, "mutationFn">
) {
  const queryClient = useQueryClient();

  return useMutation<ISaveCandidateResponseDto, ApiError, string>({
    mutationFn: saveCandidate,
    onSuccess: (data) => {
      toast.success(data.message || "Candidate saved successfully");
      queryClient.invalidateQueries({ queryKey: ["get-all-jobseekers"] }); // Refresh the saved candidates list
      queryClient.invalidateQueries({ queryKey: ["get-profile-by-id"] });
    },
    onError: (error) => {
      // Default error handler if not provided in options
      if (!options?.onError) {
        toast.error(error.response?.data?.message || "Failed to save candidate");
      }
    },
    ...options,
  });
}

// Hook for shortlisting a job applicant
export function useShortlistApplicant(
  options?: Omit<UseMutationOptions<IShortlistApplicantResponseDto, ApiError, string>, "mutationFn">
) {
  const queryClient = useQueryClient();

  return useMutation<IShortlistApplicantResponseDto, ApiError, string>({
    mutationFn: shortlistApplicant,
    onSuccess: (data, _variables) => {
      toast.success(data.message || "Applicant shortlisted successfully");

      queryClient.invalidateQueries({ queryKey: ["get-my-job-applications"] });
      queryClient.invalidateQueries({ queryKey: ["get-my-shortlisted-applications"] });
      queryClient.invalidateQueries({ queryKey: ["get-all-applicants"] });
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to shortlist applicant");
    },
    ...options,
  });
}

// Hook for rejecting a job applicant
export function useRejectApplicant(
  options?: Omit<UseMutationOptions<IRejectApplicantResponseDto, ApiError, string>, "mutationFn">
) {
  const queryClient = useQueryClient();

  return useMutation<IRejectApplicantResponseDto, ApiError, string>({
    mutationFn: rejectApplicant,
    onSuccess: (data, _variables) => {
      toast.success(data.message || "Applicant rejected successfully");

      // Force refetch all relevant queries
      queryClient.refetchQueries({ queryKey: ["get-job-applicants"] });
      queryClient.refetchQueries({ queryKey: ["get-recruiter-jobs"] });
      queryClient.refetchQueries({ queryKey: ["get-all-applicants"] });
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to reject applicant");
    },
    ...options,
  });
}

// Hook for deleting a job application
export function useDeleteApplication(
  options?: Omit<UseMutationOptions<IDeleteApplicationResponseDto, ApiError, string>, "mutationFn">
) {
  const queryClient = useQueryClient();

  return useMutation<IDeleteApplicationResponseDto, ApiError, string>({
    mutationFn: deleteApplication,
    onSuccess: (data) => {
      toast.success(data.message || "Application removed successfully");

      // Force refetch all relevant queries
      queryClient.refetchQueries({ queryKey: ["get-job-applicants"] });
      queryClient.refetchQueries({ queryKey: ["get-recruiter-jobs"] });
      queryClient.refetchQueries({ queryKey: ["get-all-applicants"] });
      queryClient.refetchQueries({ queryKey: ["get-shortlisted-applicants"] });
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to remove application");
    },
    ...options,
  });
}

export function useUnsaveCandidate(
  options?: Omit<UseMutationOptions<IUnsaveCandidateResponseDto, ApiError, string>, "mutationFn">
) {
  const queryClient = useQueryClient();

  return useMutation<IUnsaveCandidateResponseDto, ApiError, string>({
    mutationFn: unsaveCandidate,
    onSuccess: (data) => {
      toast.success(data.message || "Candidate removed from saved list");
      queryClient.invalidateQueries({ queryKey: ["get-all-jobseekers"] }); // Refresh the saved candidates list
      queryClient.invalidateQueries({ queryKey: ["get-saved-candidates"] }); // Refresh the saved candidates list
      queryClient.invalidateQueries({ queryKey: ["get-profile-by-id"] });
    },
    onError: (error) => {
      // Default error handler if not provided in options
      if (!options?.onError) {
        toast.error(error.response?.data?.message || "Failed to remove candidate from saved list");
      }
    },
    ...options,
  });
}

export function useSaveJob(
  options?: Omit<UseMutationOptions<ISaveJobResponseDto, ApiError, string>, "mutationFn">
) {
  const queryClient = useQueryClient();

  return useMutation<ISaveJobResponseDto, ApiError, string>({
    mutationFn: saveJob,
    onSuccess: (data) => {
      if (!options?.onSuccess) {
        toast.success(data.message || "Job saved successfully");
      }
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ["get-saved-jobs"] });
      queryClient.invalidateQueries({ queryKey: ["get-all-jobs"] });
    },
    onError: (error) => {
      // Default error handler if not provided in options
      if (!options?.onError) {
        toast.error(error.response?.data?.message || "Failed to save job");
      }
    },
    ...options,
  });
}

export function useUnsaveJob(
  options?: Omit<UseMutationOptions<IUnsaveJobResponseDto, ApiError, string>, "mutationFn">
) {
  const queryClient = useQueryClient();

  return useMutation<IUnsaveJobResponseDto, ApiError, string>({
    mutationFn: unsaveJob,
    onSuccess: (data) => {
      if (!options?.onSuccess) {
        toast.success(data.message || "Job removed from saved list");
      }
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ["get-saved-jobs"] });
      queryClient.invalidateQueries({ queryKey: ["get-all-jobs"] });
    },
    onError: (error) => {
      // Default error handler if not provided in options
      if (!options?.onError) {
        toast.error(error.response?.data?.message || "Failed to remove job from saved list");
      }
    },
    ...options,
  });
}

// Hook for activating/deactivating CV
export function useActivateCV(
  options?: Omit<
    UseMutationOptions<IActivateCVResponseDto, ApiError, IActivateCVRequestDto>,
    "mutationFn"
  >
) {
  const queryClient = useQueryClient();

  return useMutation<IActivateCVResponseDto, ApiError, IActivateCVRequestDto>({
    mutationFn: activateCV,
    onSuccess: (data) => {
      toast.success(data.message || "CV status updated successfully");
      queryClient.invalidateQueries({ queryKey: ["get-jobseeker-profile"] });
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to update CV status");
      // Log error in development only
      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.error("Activate CV error:", error);
      }
    },
    ...options,
  });
}

export const useExtractCVDetails = (
  options?: Omit<
    UseMutationOptions<IOCRExtractResponseDto, ApiError, IOCRExtractRequestDto>,
    "mutationFn"
  >
) => {
  return useMutation<IOCRExtractResponseDto, ApiError, IOCRExtractRequestDto>({
    mutationFn: extractCVDetails,
    ...options,
  });
};

export function useBoostJob(
  options?: Omit<
    UseMutationOptions<IBoostJobResponseDto, ApiError, IBoostJobRequestDto>,
    "mutationFn"
  >
) {
  const queryClient = useQueryClient();

  return useMutation<IBoostJobResponseDto, ApiError, IBoostJobRequestDto>({
    mutationFn: boostJob,
    onSuccess: (data, variables) => {
      // toast.success(data.message || "Job boosted successfully");
      queryClient.invalidateQueries({ queryKey: ["get-recruiter-jobs"] });
      queryClient.invalidateQueries({ queryKey: ["get-job-by-id", variables.jobId] });
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to boost job");
      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.error("Boost job error:", error);
      }
    },
    ...options,
  });
}

export function usePurchasePro(
  options?: Omit<
    UseMutationOptions<IPurchaseProResponseDto, ApiError, IPurchaseProRequestDto>,
    "mutationFn"
  >
) {
  const queryClient = useQueryClient();

  return useMutation<IPurchaseProResponseDto, ApiError, IPurchaseProRequestDto>({
    mutationFn: purchasePro,
    onSuccess: (data) => {
      toast.success(data.message || "Pro membership purchased successfully");
      queryClient.invalidateQueries({ queryKey: ["get-admin-settings"] }); // Assuming this query fetches pro membership status
      queryClient.invalidateQueries({ queryKey: ["get-current-user"] }); // Assuming this query fetches pro membership status
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to purchase pro membership");
      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.error("Purchase pro error:", error);
      }
    },
    ...options,
  });
}
