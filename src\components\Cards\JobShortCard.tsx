"use client";
import Image from "next/image";
import Link from "next/link";
import React, { useEffect, useState } from "react";
import SavedRemovedJobBtn from "../SavedRemovedJobBtn";
import { useUserStore } from "@/store/useUserStore";

interface JobShortCardProps {
  _jobId: string;
  imageUrl: string;
  jobTitle: string;
  companyName: string;
  category: string;
  jobType: string;
  cityName: string;
  salaryRange: string;
  deadline: string;
  detailLink?: string;
  isSaved?: boolean;
  premiumExpireAt?: string;
}

const JobShortCard: React.FC<JobShortCardProps> = ({
  _jobId,
  imageUrl,
  jobTitle,
  companyName,
  category,
  jobType,
  cityName,
  salaryRange,
  deadline,
  detailLink = "#",
  isSaved = false,
  premiumExpireAt,
}) => {
  const [isMobile, setIsMobile] = useState(false);
  const isPremiumJob = premiumExpireAt ? new Date(premiumExpireAt) > new Date() : false;
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 1023); // lg breakpoint is 1024px
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);
  const { currentUser } = useUserStore();
  const cardContent = (
    <div className="rounded-[18px] border border-gray-200 lg:p-[20px] p-2">
      <div className="flex">
        <div className="mr-4 w-[25%]">
          <Image
            src={imageUrl}
            alt="Company Logo"
            width={80}
            height={80}
            className="w-[80px] h-[80px] rounded-full"
          />
        </div>
        <div className="w-[75%]">
          {jobTitle && <h2 className="text-2xl font-medium text-black-100 mb-3">{jobTitle}</h2>}
          {(companyName || category) && (
            <p className="text-black-100">
              {companyName && (
                <>
                  <span className="text-gray-100">by</span> {companyName}
                </>
              )}{" "}
              {category && (
                <>
                  <span className="text-gray-100">in</span> {category}
                </>
              )}
            </p>
          )}
        </div>
      </div>
      {(jobType || cityName || salaryRange) && (
        <div className="flex gap-4 flex-wrap mt-6">
          {jobType && (
            <div className="bg-offWhite-100 text-orange-100 px-6 py-3 rounded-full">{jobType}</div>
          )}
          {cityName && (
            <div className="bg-white text-gray-100 border border-gray-100 px-6 py-3 rounded-full">
              {cityName}
            </div>
          )}
          {salaryRange && (
            <div className="bg-white text-gray-100 border border-gray-100 px-6 py-3 rounded-full">
              {salaryRange}
            </div>
          )}
        </div>
      )}
      <div className="flex justify-between mt-6 flex-wrap gap-2">
        {deadline && (
          <div className="text-orange-100">
            Deadline Date: <span className="text-black-100">{deadline}</span>{" "}
          </div>
        )}
        <div className="flex space-x-2">
          {/* {isPremiumJob && (
            <span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  fill={isPremiumJob ? "#FF6B00" : "#262626"}
                  d="M21.647 6.9a1.486 1.486 0 0 0-1.772.355l-3.157 3.403-3.356-7.528v-.01a1.5 1.5 0 0 0-2.724 0v.01l-3.356 7.528-3.157-3.403A1.5 1.5 0 0 0 1.53 8.543l2.126 9.738A1.5 1.5 0 0 0 5.13 19.5h13.74a1.5 1.5 0 0 0 1.474-1.219l2.126-9.738q-.002-.015.007-.03a1.486 1.486 0 0 0-.83-1.613m-2.77 11.07-.006.03H5.129l-.006-.03L3 8.25l.013.015 3.938 4.241a.75.75 0 0 0 1.235-.204L12 3.75l3.815 8.555a.75.75 0 0 0 1.235.204l3.938-4.241L21 8.25z"
                ></path>
              </svg>
            </span>
          )} */}
          {isPremiumJob && (
            <span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  fill={isPremiumJob ? "#FF6B00" : "#262626"}
                  d="M20.23 11.079a.75.75 0 0 0-.468-.531L14.36 8.522l1.374-6.875a.75.75 0 0 0-1.283-.656l-10.5 11.25a.75.75 0 0 0 .28 1.219l5.404 2.026-1.371 6.867a.75.75 0 0 0 1.283.656l10.5-11.25a.75.75 0 0 0 .182-.68m-9.977 8.984.982-4.911a.75.75 0 0 0-.469-.85l-4.954-1.86 7.934-8.5-.981 4.91a.75.75 0 0 0 .469.85l4.95 1.857z"
                ></path>
              </svg>
            </span>
          )}
          {currentUser && <SavedRemovedJobBtn jobId={_jobId} isSaved={isSaved} />}
        </div>
      </div>
    </div>
  );

  return isMobile ? (
    <Link className="block" href={detailLink}>
      {cardContent}
    </Link>
  ) : (
    <>{cardContent}</>
  );
};

export default JobShortCard;
