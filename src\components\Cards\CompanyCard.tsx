import Image from "next/image";
import React from "react";
import SecondaryButton from "../Buttons/SecondaryButton";

interface CompanyCardProps {
  companyName: string;
  description: string;
  imageUrl: string;
  href: string;
}

const CompanyCard: React.FC<CompanyCardProps> = ({ companyName, description, imageUrl, href }) => {
  return (
    <div className="shadow-lg inset-shadow-2xs rounded-xl p-4 my-4 ">
      <div className="flex items-center border-b-2 py-5 gap-x-3 text-gray-100 h-[100px]">
        <div>
          <Image
            src={imageUrl}
            alt={`${companyName} logo`}
            width={40}
            height={40}
            className="w-[50px] h-[50px] rounded-full object-cover"
          />
        </div>
        <div>
          <h2 className="text-xl font-medium text-black-100 w-28 truncate">{companyName}</h2>
        </div>
      </div>
      <div className="h-[150px]">
        <p className="text-gray-100 py-7 line-clamp-3 ">{description}</p>
      </div>
      <SecondaryButton variant="borderless" href={href}>
        View
      </SecondaryButton>
    </div>
  );
};

export default CompanyCard;
