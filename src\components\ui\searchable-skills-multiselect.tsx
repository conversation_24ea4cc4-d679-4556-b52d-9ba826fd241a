"use client";

import { ChevronDown, Search, X } from "lucide-react";
import { useState, useEffect, useRef, useCallback } from "react";
import { useGetSkills } from "@/hooks/useQuery";
import type { ISkill } from "@/types/query.types";

interface SearchableSkillsMultiSelectProps {
  value: string[];
  onValueChange: (value: string[]) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function SearchableSkillsMultiSelect({
  value = [],
  onValueChange,
  placeholder = "Select skills",
  className = "",
  disabled = false,
}: SearchableSkillsMultiSelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(1);
  const [allSkills, setAllSkills] = useState<ISkill[]>([]);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const listRef = useRef<HTMLDivElement>(null);

  // Debounced search term
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Fetch skills with search and pagination
  const { data: skillsData, isLoading } = useGetSkills(
    {
      search: debouncedSearchTerm,
      page: page,
      limit: 20,
    },
    {
      enabled: isOpen,
    }
  );

  // Update skills list when data changes
  useEffect(() => {
    if (skillsData?.data) {
      if (page === 1) {
        // Reset list for new search or first page
        setAllSkills(skillsData.data.skills);
      } else {
        // Append to existing list for pagination
        setAllSkills((prev) => [...prev, ...skillsData.data.skills]);
      }
      setHasNextPage(skillsData.data.pagination.hasNextPage);
      setIsLoadingMore(false);
    }
  }, [skillsData, page]);

  // Reset page when search term changes
  useEffect(() => {
    setPage(1);
    if (debouncedSearchTerm) {
      setAllSkills([]);
    }
  }, [debouncedSearchTerm]);

  // Handle scroll for infinite loading
  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

      if (
        scrollHeight - scrollTop <= clientHeight + 10 &&
        hasNextPage &&
        !isLoading &&
        !isLoadingMore
      ) {
        setIsLoadingMore(true);
        setPage((prev) => prev + 1);
      }
    },
    [hasNextPage, isLoading, isLoadingMore]
  );

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm("");
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  const handleToggleSkill = (skill: ISkill) => {
    const isSelected = value.includes(skill.skill);
    if (isSelected) {
      onValueChange(value.filter((s) => s !== skill.skill));
    } else {
      onValueChange([...value, skill.skill]);
    }
  };

  const handleRemoveSkill = (skillToRemove: string, e: React.MouseEvent) => {
    e.stopPropagation();
    onValueChange(value.filter((skill) => skill !== skillToRemove));
  };

  const handleClearAll = (e: React.MouseEvent) => {
    e.stopPropagation();
    onValueChange([]);
  };

  const selectedCount = value.length;
  const displayText =
    selectedCount === 0
      ? placeholder
      : `${selectedCount} skill${selectedCount === 1 ? "" : "s"} selected`;

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {selectedCount > 0 && (
        <div className="flex flex-wrap gap-2 mb-3">
          {value.map((skill) => (
            <span
              key={skill}
              className="inline-flex items-center gap-1 px-3 py-1 bg-orange-50 text-orange-600 border border-orange-200 rounded-full text-sm"
            >
              {skill}
              {!disabled && (
                <X
                  className="h-3 w-3 cursor-pointer hover:text-orange-800"
                  onClick={(e) => handleRemoveSkill(skill, e)}
                />
              )}
            </span>
          ))}
        </div>
      )}
      {/* Trigger Button */}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`
          w-full h-[60px] px-4 rounded-full border border-gray-300 shadow
          flex items-center justify-between bg-white text-left
          ${disabled ? "opacity-50 cursor-not-allowed" : "hover:border-gray-400 cursor-pointer"}
          ${isOpen ? "border-orange-500 ring-1 ring-orange-500" : ""}
        `}
      >
        <span className={selectedCount > 0 ? "text-gray-900" : "text-gray-500"}>{displayText}</span>
        <div className="flex items-center gap-2">
          {selectedCount > 0 && !disabled && (
            <X className="h-4 w-4 text-gray-400 hover:text-gray-600" onClick={handleClearAll} />
          )}
          <ChevronDown
            className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? "rotate-180" : ""}`}
          />
        </div>
      </button>

      {/* Selected Skills Display */}

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-2 bg-white border border-gray-300 rounded-lg shadow-lg max-h-80 overflow-hidden">
          {/* Search Input */}
          <div className="p-3 border-b border-gray-200">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search skills..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              />
            </div>
          </div>

          {/* Skills List */}
          <div ref={listRef} className="max-h-60 overflow-y-auto" onScroll={handleScroll}>
            {isLoading && page === 1 ? (
              <div className="p-4 text-center text-gray-500">Loading skills...</div>
            ) : allSkills.length > 0 ? (
              <>
                {allSkills.map((skill) => {
                  const isSelected = value.includes(skill.skill);
                  return (
                    <button
                      key={skill._id}
                      type="button"
                      onClick={() => handleToggleSkill(skill)}
                      className={`
                        w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors flex items-center justify-between
                        ${isSelected ? "bg-orange-50 text-orange-600" : "text-gray-900"}
                      `}
                    >
                      <span>{skill.skill}</span>
                      {isSelected && (
                        <div className="w-4 h-4 bg-orange-500 rounded-sm flex items-center justify-center">
                          <X className="w-3 h-3 text-white" />
                        </div>
                      )}
                    </button>
                  );
                })}
                {isLoadingMore && (
                  <div className="p-4 text-center text-gray-500">Loading more...</div>
                )}
              </>
            ) : (
              <div className="p-4 text-center text-gray-500">
                {debouncedSearchTerm ? "No skills found" : "No skills available"}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
