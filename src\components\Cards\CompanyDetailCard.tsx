import Image from "next/image";
import Link from "next/link";
import React from "react";
// import PrimaryButton from "../Buttons/PrimaryButton";

// Helper function to convert YouTube URLs to embed format
// const getEmbedUrl = (url: string): string => {
//   // Handle YouTube URLs
//   if (url.includes("youtube.com") || url.includes("youtu.be")) {
//     // Extract video ID
//     let videoId = "";

//     if (url.includes("youtube.com/watch")) {
//       const urlParams = new URLSearchParams(url.split("?")[1]);
//       videoId = urlParams.get("v") || "";
//     } else if (url.includes("youtu.be")) {
//       videoId = url.split("/").pop() || "";
//     }

//     if (videoId) {
//       return `https://www.youtube.com/embed/${videoId}`;
//     }
//   }

//   // If not a recognized format or couldn't extract ID, return original URL
//   return url;
// };
import {
  CalenderIcon,
  // ChatDots,
  EnvelopeSimpleIcon,
  MapPin,
  Trophy,
  UsersThreeIcon,
} from "../Icons";
import CandidateDetailInfo from "./CandidateDetailInfo";

interface CompanyDetailCardProps {
  imageUrl: string;
  comanyName: string;
  category: string;
  openJobs: number;
  cityName: string;
  salaryRange: string;
  // skillsExperience: string[];
  aboutUs: string;
  // skillsTags: string[];
  perksBenefits: string[];
  designation: string;
  founded: string;
  location: string;
  companySize: string;
  qualification: string;
  email: string;
  // linkdinLink: string;
  // instaLink: string;
  // Xlink: string;
  companyPhotos: string[];
  companyVideoUrl?: string;
  socialNetworks?: Array<{
    networkName: string;
    networkUrl: string;
  }>;
  achievements?: Array<{
    title: string;
    date: string;
    eventOrInstitute: string;
    detail: string;
  }>;
}

const CompanyDetailCard: React.FC<CompanyDetailCardProps> = ({
  imageUrl,
  comanyName,
  openJobs,
  cityName,
  aboutUs,
  founded,
  location,
  companySize,
  email,
  // linkdinLink,
  // instaLink,
  // Xlink,
  perksBenefits,
  companyPhotos,
  companyVideoUrl,
  achievements,
  socialNetworks,
  // skillsExperience,
  // skillsTags,
}) => {
  return (
    <>
      <div className={`rounded-[18px] border bg-offWhite-100 border-gray-200 p-[30px]`}>
        <div className="flex items-center">
          <div className="mr-4">
            <Image
              src={imageUrl}
              alt="Company Logo"
              width={80}
              height={80}
              className="w-[80px] h-[80px] rounded-full"
            />
          </div>
          <div>
            <h2 className="text-2xl font-medium text-black-100 mb-3">{comanyName}</h2>
          </div>
          {/* <div className="flex space-x-4 ml-auto">
            <span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  fill="#262626"
                  d="M21.647 6.9a1.486 1.486 0 0 0-1.772.355l-3.157 3.403-3.356-7.528v-.01a1.5 1.5 0 0 0-2.724 0v.01l-3.356 7.528-3.157-3.403A1.5 1.5 0 0 0 1.53 8.543l2.126 9.738A1.5 1.5 0 0 0 5.13 19.5h13.74a1.5 1.5 0 0 0 1.474-1.219l2.126-9.738q-.002-.015.007-.03a1.486 1.486 0 0 0-.83-1.613m-2.77 11.07-.006.03H5.129l-.006-.03L3 8.25l.013.015 3.938 4.241a.75.75 0 0 0 1.235-.204L12 3.75l3.815 8.555a.75.75 0 0 0 1.235.204l3.938-4.241L21 8.25z"
                ></path>
              </svg>
            </span>
            <span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  fill="#262626"
                  d="M20.23 11.079a.75.75 0 0 0-.468-.531L14.36 8.522l1.374-6.875a.75.75 0 0 0-1.283-.656l-10.5 11.25a.75.75 0 0 0 .28 1.219l5.404 2.026-1.371 6.867a.75.75 0 0 0 1.283.656l10.5-11.25a.75.75 0 0 0 .182-.68m-9.977 8.984.982-4.911a.75.75 0 0 0-.469-.85l-4.954-1.86 7.934-8.5-.981 4.91a.75.75 0 0 0 .469.85l4.95 1.857z"
                ></path>
              </svg>
            </span>
            <span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  fill="#262626"
                  d="M17.25 3H6.75a1.5 1.5 0 0 0-1.5 1.5V21a.75.75 0 0 0 1.148.636L12 18.134l5.603 3.502A.75.75 0 0 0 18.75 21V4.5a1.5 1.5 0 0 0-1.5-1.5m0 1.5v10.647l-4.853-3.033a.75.75 0 0 0-.795 0L6.75 15.146V4.5zm-4.853 12.114a.75.75 0 0 0-.795 0L6.75 19.647v-2.732l5.25-3.28 5.25 3.28v2.732z"
                ></path>
              </svg>
            </span>
          </div> */}
        </div>
        <div className="flex gap-4 flex-wrap mt-6">
          <div className="bg-white border border-orange-100 text-orange-100 px-6 py-3 rounded-full">
            Open Jobs - {openJobs}
          </div>
          <div className="bg-white text-gray-100 border border-gray-100 px-6 py-3 rounded-full">
            {cityName}
          </div>
        </div>
      </div>
      <div className="flex mt-8">
        <div className="mr-14 w-[75%]">
          {aboutUs && (
            <div>
              <h2 className="text-2xl font-bold text-black-100 mb-3">About Us</h2>
              <p className="text-base font-normal text-gray-100 leading-6 break-words">{aboutUs}</p>
            </div>
          )}
          {companyVideoUrl && (
            <div className="my-6">
              <h2 className="text-2xl font-bold text-black-100 mb-3">Intro Video</h2>
              <div className=" rounded-lg overflow-hidden">
                {/* <iframe
                  src={getEmbedUrl(companyVideoUrl)}
                  title="Company Video"
                  className="w-full h-full"
                  allowFullScreen
                  style={{ width: "100%", height: "400px" }}
                ></iframe> */}
                <video
                  src={companyVideoUrl}
                  className="w-full h-[400px] object-cover"
                  controlsList="nodownload noplaybackrate"
                  disablePictureInPicture
                  controls
                  playsInline
                  preload="metadata"
                  onContextMenu={(e) => e.preventDefault()}
                >
                  <source src={companyVideoUrl} type="video/mp4" />
                  <source src={companyVideoUrl} type="video/webm" />
                  Your browser does not support the video tag.
                </video>
              </div>
            </div>
          )}

          {achievements && achievements.length > 0 && (
            <div className="my-6">
              <h2 className="text-2xl font-bold text-black-100 mb-3">Achievements</h2>
              {achievements.map((achievement, index) => (
                <div className="mb-3" key={index}>
                  <CandidateDetailInfo
                    date={new Date(achievement.date).toLocaleDateString("en-US", {
                      year: "numeric",
                      month: "long",
                    })}
                    description={achievement.eventOrInstitute}
                    title={achievement.title}
                    icon={<Trophy />}
                  />
                </div>
              ))}
            </div>
          )}
          {perksBenefits && perksBenefits.length > 0 && (
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-black-100 mb-3">Perks & Benefits</h2>
              <ul className="mt-4">
                {perksBenefits.map((item, index) => (
                  <li
                    key={index}
                    className="text-base font-normal text-gray-100 mb-3 flex items-center gap-x-2"
                  >
                    <span className="w-[5px] h-[5px] bg-gray-100 rounded-full flex justify-center items-center"></span>{" "}
                    {item}
                  </li>
                ))}
              </ul>
            </div>
          )}
          {companyPhotos && companyPhotos.length > 0 && (
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-black-100 mb-4">Company Photos</h2>
              <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-4">
                {companyPhotos.map((photo, index) => (
                  <Image
                    src={photo}
                    key={index}
                    width={0}
                    height={0}
                    layout="responsive"
                    style={{ width: "100%", height: "200px" }}
                    alt={`Company photo ${index + 1}`}
                  />
                ))}
              </div>
            </div>
          )}
          {/* Skills and Experience Section */}
          {/* {skillsExperience && skillsExperience.length > 0 && (
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-black-100 mb-4">Skills & Experience</h2>
              <ul className="mt-4">
                {skillsExperience.map((item, index) => (
                  <li
                    key={index}
                    className="text-base font-normal text-gray-100 mb-3 flex items-center gap-x-2"
                  >
                    <span className="w-[5px] h-[5px] bg-gray-100 rounded-full flex justify-center items-center"></span>{" "}
                    {item}
                  </li>
                ))}
              </ul>
            </div>
          )} */}

          {/* Skills Tags Section */}
          {/* {skillsTags && skillsTags.length > 0 && (
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-black-100 mb-4">Skills Tags</h2>
              <div className="flex flex-wrap gap-2">
                {skillsTags.map((tag, index) => (
                  <span
                    key={index}
                    className="bg-white border border-orange-100 text-orange-100 px-4 py-2 rounded-full text-sm"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )} */}
        </div>
        <div className="space-y-7 shadow w-[22%] rounded-2xl border h-fit px-6 py-8">
          <div className="flex items-center">
            <div className="bg-offWhite-100 w-[60px] h-[60px] flex justify-center items-center rounded-full mr-3">
              <CalenderIcon />
            </div>
            <div>
              <span className="text-black-100 font-medium block mb-1">Founded</span>
              <span className="text-gray-100 font-normal block">{founded}</span>
            </div>
          </div>
          <div className="flex items-center">
            <div>
              <div className="bg-offWhite-100 w-[60px] h-[60px] flex justify-center items-center rounded-full mr-3">
                <MapPin />
              </div>
            </div>
            <div>
              <span className="text-black-100 font-medium block mb-1">Location</span>
              <span className="text-gray-100 font-normal block">{location}</span>
            </div>
          </div>
          <div className="flex items-center">
            <div className="bg-offWhite-100 w-[60px] h-[60px] flex justify-center items-center rounded-full mr-3">
              <UsersThreeIcon />
            </div>
            <div>
              <span className="text-black-100 font-medium block mb-1">Company Size</span>
              <span className="text-gray-100 font-normal block">{companySize}</span>
            </div>
          </div>

          <div className="flex items-center">
            <div className="bg-offWhite-100 w-[60px] h-[60px] flex justify-center items-center rounded-full mr-3">
              <EnvelopeSimpleIcon />
            </div>
            <div className="w-28">
              <span className="text-black-100 font-medium block mb-1">Email</span>
              <span className="text-gray-100 font-normal block break-words whitespace-normal">
                {email}
              </span>
            </div>
          </div>
          <div className="border-t pt-4 ">
            <h3 className="text-black-100 font-bold mb-4">Social Profiles</h3>
            <div className="flex flex-wrap text-orange-100 gap-x-4"></div>
            {socialNetworks?.map((network) => (
              <div key={network?.networkName}>
                <Link
                  href={network?.networkUrl}
                  className="text-orange-100"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {network?.networkName}
                </Link>
              </div>
            ))}
          </div>
          {/* <div className="flex text-orange-100 gap-x-4">
              <Link href={linkdinLink}>
                <LinkdinIcon />
              </Link>
              <Link href={instaLink}>
                <InstagramIcon />
              </Link>
              <Link href={Xlink}>
                <XIcon />
              </Link>
            </div> */}
        </div>
      </div>
    </>
  );
};

export default CompanyDetailCard;
