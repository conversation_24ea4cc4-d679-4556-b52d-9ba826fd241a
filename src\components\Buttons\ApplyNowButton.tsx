"use client";

// import Link from "next/link";
import { useRouter, usePathname } from "next/navigation";
import React from "react";
import { toast } from "sonner";
import { LeftIconTop } from "../Icons";
import { useApplyJob } from "@/hooks/useMutation";
import { useUserStore } from "@/store/useUserStore";

interface ApplyNowButtonProps {
  jobId: string;
  disabled?: boolean;
  onSuccess?: () => void;
}

const ApplyNowButton: React.FC<ApplyNowButtonProps> = ({ jobId, disabled = false, onSuccess }) => {
  const { mutate: applyJob, isPending } = useApplyJob({
    onSuccess: (data) => {
      toast.success(data.message || "Application submitted successfully");
      if (onSuccess) onSuccess();
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to apply for this job");
    },
  });

  const { currentUser } = useUserStore();
  const router = useRouter();
  const pathname = usePathname();

  const handleApply = () => {
    if (!currentUser) {
      // Save current location for redirect after login
      sessionStorage.setItem("redirectAfterLogin", pathname);
      router.push("/login");
      return;
    }
    applyJob(jobId);
  };

  return (
    <button
      onClick={handleApply}
      disabled={disabled || isPending}
      className={`py-3 px-8 rounded-full inline-flex items-center justify-center space-x-2 ${
        disabled || isPending
          ? "bg-gray-300 cursor-not-allowed"
          : "bg-orange-100 hover:bg-orange-200"
      } text-white transition-colors`}
    >
      <span className="text">
        {isPending ? "Applying..." : currentUser ? "Apply Now" : "Login to Apply"}
      </span>
      <span className="icon">
        <LeftIconTop />
      </span>
    </button>
  );
};

export default ApplyNowButton;
