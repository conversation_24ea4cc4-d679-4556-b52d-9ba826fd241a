"use client";
import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm, type SubmitHandler } from "react-hook-form";
import { toast } from "sonner";
import usePlacesAutocomplete, { getGeocode, getLatLng } from "use-places-autocomplete";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useUpdateJobSeekerProfile } from "@/hooks/useMutation";
import { useGetJobSeekerProfile, useGetAllEnums } from "@/hooks/useQuery";
import type { IJobPreferences } from "@/types/query.types";

const inputClasses = "h-12 px-4 rounded-full border border-gray-300 w-full";

const JobPreferences = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { data: profileData } = useGetJobSeekerProfile();
  const { data: enumsData, isLoading: isEnumsLoading, error: enumsError } = useGetAllEnums();

  const [coordinates, setCoordinates] = useState({ lat: 0, lng: 0 });
  const [formattedAddress, setFormattedAddress] = useState("");
  const [city, setCity] = useState("");
  const [state, setState] = useState("");
  const [country, setCountry] = useState("");

  const {
    ready,
    value,
    setValue,
    suggestions: { status, data },
    clearSuggestions,
  } = usePlacesAutocomplete({
    requestOptions: {
      componentRestrictions: { country: "au" },
    },
  });

  const { mutate: updateProfile, isPending } = useUpdateJobSeekerProfile({
    onSuccess: () => {
      toast.success("Profile updated successfully");
      queryClient.invalidateQueries({ queryKey: ["get-jobseeker-profile"] });
      router.push("/profile-completed");
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to update profile");
    },
  });

  const [jobCategoryError, setJobCategoryError] = useState<string | null>(null);
  const [jobTypeError, setJobTypeError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    setValue: setFormValue,
    formState: { errors },
  } = useForm<IJobPreferences>({
    defaultValues: {
      jobCategory: profileData?.data.jobPreferences?.jobCategory || [],
      jobType: profileData?.data.jobPreferences?.jobType || "",
      salaryRangeStart: profileData?.data.jobPreferences?.salaryRangeStart || 0,
      salaryRangeEnd: profileData?.data.jobPreferences?.salaryRangeEnd || 0,
      location: {
        type: "Point",
        coordinates: [
          profileData?.data.jobPreferences?.location?.coordinates?.[0] || 0,
          profileData?.data.jobPreferences?.location?.coordinates?.[1] || 0,
        ],
        formattedAddress: profileData?.data.jobPreferences?.location?.formattedAddress || "",
        city: profileData?.data.jobPreferences?.location?.city || "",
        state: profileData?.data.jobPreferences?.location?.state || "",
        country: profileData?.data.jobPreferences?.location?.country || "",
      },
    },
  });

  const handleSelect = async (address: string) => {
    setValue(address, false);
    clearSuggestions();

    try {
      const results = await getGeocode({ address });
      const { lat, lng } = getLatLng(results[0]);
      const addressComponents = results[0].address_components;

      // Define a type for address components
      interface AddressComponent {
        long_name: string;
        short_name: string;
        types: string[];
      }

      const city =
        addressComponents.find((c: AddressComponent) => c.types.includes("locality"))?.long_name ||
        "";
      const state =
        addressComponents.find((c: AddressComponent) =>
          c.types.includes("administrative_area_level_1")
        )?.long_name || "";
      const country =
        addressComponents.find((c: AddressComponent) => c.types.includes("country"))?.long_name ||
        "";

      setCoordinates({ lat, lng });
      setFormattedAddress(address);
      setCity(city);
      setState(state);
      setCountry(country);

      setFormValue("location", {
        type: "Point",
        coordinates: [lat, lng],
        formattedAddress: address,
        city,
        state,
        country,
      });
    } catch {
      toast.error("Failed to get location details");
    }
  };

  const onSubmit: SubmitHandler<IJobPreferences> = async (data) => {
    let hasError = false;

    // Check if job category and job type are selected
    if (!data.jobCategory || data.jobCategory.length === 0) {
      setJobCategoryError("Job Category is required");
      hasError = true;
    }

    if (!data.jobType) {
      setJobTypeError("Job Type is required");
      hasError = true;
    }

    // Additional validation before submission
    if (data.salaryRangeStart <= 0 || data.salaryRangeEnd <= 0) {
      toast.error("Salary must be greater than 0");
      hasError = true;
    }

    if (data.salaryRangeStart >= data.salaryRangeEnd) {
      toast.error("End salary must be greater than start salary");
      hasError = true;
    }

    if (data.salaryRangeStart > 1000000 || data.salaryRangeEnd > 1000000) {
      toast.error("Salary cannot exceed 1,000,000");
      hasError = true;
    }

    if (!formattedAddress) {
      toast.error("Please select a location");
      hasError = true;
    }

    if (hasError) {
      return;
    }

    try {
      updateProfile({
        jobPreferences: {
          ...data,
          location: {
            type: "Point",
            coordinates: [coordinates.lng, coordinates.lat], // Ensure correct order
            formattedAddress,
            city,
            state,
            country,
          },
        },
      });
    } catch {
      toast.error("Failed to save job preferences");
    }
  };

  if (isEnumsLoading) {
    return <div>Loading...</div>;
  }

  if (enumsError) {
    return <div>Error loading enums data. Please try again later.</div>;
  }

  const jobCategories = enumsData?.data.JOB_CATEGORIES_ENUM
    ? Object.entries(enumsData.data.JOB_CATEGORIES_ENUM).map(([key, value]) => ({
        label: key.replace(/_/g, " "), // Format category names
        value,
      }))
    : [];

  const jobTypes = enumsData?.data.JOB_TYPE_ENUM
    ? Object.entries(enumsData.data.JOB_TYPE_ENUM).map(([key, value]) => ({
        label: key.replace(/_/g, " "), // Format job type names
        value,
      }))
    : [];

  return (
    <div>
      <h2 className="text-blue-200 text-2xl font-bold">Job Preferences</h2>
      <h3 className="text-4xl font-medium text-black-100 mt-3">Set your Job Alerts</h3>
      <form onSubmit={handleSubmit(onSubmit)} className="mt-10">
        {/* Job Category */}
        <div className="mb-6">
          <Label htmlFor="jobCategory" className="mb-3 block">
            Select Job Category  
          </Label>
          <Select
            onValueChange={(value) => {
              setFormValue("jobCategory", [value], {
                shouldValidate: true,
                shouldDirty: true,
              });
              setJobCategoryError(null);
            }}
            defaultValue={profileData?.data.jobPreferences?.jobCategory?.[0]}
          >
            <SelectTrigger
              className={`w-full bg-white rounded-full h-[46px] px-6 text-gray-100 text-base ${jobCategoryError ? "border-red-500 border-2" : ""}`}
            >
              <SelectValue placeholder="Select a Job Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {jobCategories.map(({ label, value }) => (
                  <SelectItem key={value} value={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
          {jobCategoryError && <p className="text-red-500 text-sm mt-1">{jobCategoryError}</p>}
        </div>

        {/* Job Type */}
        <div className="mb-6">
          <Label htmlFor="jobType" className="mb-3 block">
            Select Job Type
          </Label>
          <Select
            onValueChange={(value) => {
              setFormValue("jobType", value, {
                shouldValidate: true,
                shouldDirty: true,
              });
              setJobTypeError(null);
            }}
            defaultValue={profileData?.data.jobPreferences?.jobType}
          >
            <SelectTrigger
              className={`w-full bg-white rounded-full h-[46px] px-6 text-gray-100 text-base ${jobTypeError ? "border-red-500 border-2" : ""}`}
            >
              <SelectValue placeholder="Select a Job Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {jobTypes.map(({ label, value }) => (
                  <SelectItem key={value} value={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
          {jobTypeError && <p className="text-red-500 text-sm mt-1">{jobTypeError}</p>}
        </div>

        {/* Salary Range */}
        <div className="mb-6">
          <Label htmlFor="salaryRangeStart" className="mb-3 block">
            Salary Range Start
          </Label>
          <Input
            id="salaryRangeStart"
            type="number"
            className={inputClasses}
            {...register("salaryRangeStart", {
              required: "Salary range start is required",
              valueAsNumber: true,
              min: {
                value: 1,
                message: "Salary must be greater than 0",
              },
              max: {
                value: 1000000,
                message: "Salary cannot exceed 1,000,000",
              },
            })}
          />
          {errors.salaryRangeStart && (
            <p className="text-red-500 text-sm">{errors.salaryRangeStart.message}</p>
          )}
        </div>

        <div className="mb-6">
          <Label htmlFor="salaryRangeEnd" className="mb-3 block">
            Salary Range End
          </Label>
          <Input
            id="salaryRangeEnd"
            type="number"
            className={inputClasses}
            {...register("salaryRangeEnd", {
              required: "Salary range end is required",
              valueAsNumber: true,
              min: {
                value: 1,
                message: "Salary must be greater than 0",
              },
              max: {
                value: 1000000,
                message: "Salary cannot exceed 1,000,000",
              },
              validate: (value) => {
                // Get the current value of salaryRangeStart from the form
                const startSalaryInput = document.getElementById(
                  "salaryRangeStart"
                ) as HTMLInputElement;
                const startSalary = startSalaryInput
                  ? Number.parseFloat(startSalaryInput.value)
                  : 0;
                return value > startSalary || "End salary must be greater than start salary";
              },
            })}
          />
          {errors.salaryRangeEnd && (
            <p className="text-red-500 text-sm">{errors.salaryRangeEnd.message}</p>
          )}
        </div>

        {/* Location */}
        <div className="mb-6">
          <Label htmlFor="location" className="mb-3 block">
            Location
          </Label>
          <div className="relative">
            <Input
              className={inputClasses}
              id="location"
              placeholder="e.g., New York, Remote"
              value={value}
              onChange={(e) => setValue(e.target.value)}
              disabled={!ready}
            />
            {status === "OK" && (
              <ul className="absolute z-10 w-full bg-white border border-gray-300 rounded-lg mt-2 max-h-60 overflow-y-auto">
                {data.map(({ place_id, description }) => (
                  <li
                    key={place_id}
                    className="p-2 cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSelect(description)}
                  >
                    {description}
                  </li>
                ))}
              </ul>
            )}
          </div>
          {errors.location && <p className="text-red-500 text-sm">{errors.location.message}</p>}
        </div>

        {/* Submit Buttons */}
        <div className="flex gap-5">
          <button
            disabled={isPending}
            onClick={() => router.back()}
            type="button"
            className="font-bold py-4 px-10 text-black font-base rounded-full inline-flex items-center justify-center space-x-2 bg-[#E7E7E7]"
          >
            Go Back
          </button>
          <button
            disabled={isPending}
            type="submit"
            className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-orange-100 text-white"
          >
            {isPending ? "Loading..." : "Save Details"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default JobPreferences;
