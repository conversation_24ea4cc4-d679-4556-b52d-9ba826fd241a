"use client";
import { DropdownMenuItem } from "@radix-ui/react-dropdown-menu";
import { useQueryClient } from "@tanstack/react-query";
import { Ellipsis } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { toast } from "sonner";
import { Button } from "../ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "../ui/dropdown-menu";
import {
  ChatDots,
  CheckeDouble,
  CrossRounded,
  FileArrowDown,
  Prohibitlnst,
} from "@/components/Icons";
import LoadingSpinner from "@/components/LoadingSpinner/LoadingSpinner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { DEFAULT_IMAGE } from "@/constants/app.constants";
import {
  useCreateConversation,
  useDeleteApplication,
  useRejectApplicant,
  useShortlistApplicant,
} from "@/hooks/useMutation";

interface TableRowData {
  name?: string;
  jobTitle?: string;
  designation?: string;
  appliedDate?: string;
  imageUrl?: string;
  id?: string;
  jobSeekerId?: string;
  status?: string;
  cvUrl?: string;
}

// Helper function to get status badge color
const getStatusBadgeColor = (status?: string) => {
  switch (status) {
    case "SHORTLISTED":
      return "bg-green-100 text-green-800";
    case "REJECTED":
      return "bg-red-100 text-red-800";
    case "PENDING":
    default:
      return "bg-yellow-100 text-yellow-800";
  }
};

interface CompanyDashboardTableProps {
  data?: TableRowData[];
  hideShortlistAction?: boolean;
}

const CompanyDashboardTable: React.FC<CompanyDashboardTableProps> = ({
  data,
  hideShortlistAction = false,
}) => {
  const [loadingStates, setLoadingStates] = useState<Record<string, string>>({});
  const queryClient = useQueryClient();
  const router = useRouter();
  // Initialize hooks with proper success and error handling
  const { mutate: shortlistApplicant } = useShortlistApplicant({
    onSuccess: (data) => {
      toast.success(data.message || "Applicant shortlisted successfully");
      setLoadingStates((prev) => ({
        ...prev,
        [data.data.application._id]: "",
      }));

      queryClient.refetchQueries({ queryKey: ["get-job-applicants"] });
      queryClient.refetchQueries({ queryKey: ["get-recruiter-jobs"] });
      queryClient.refetchQueries({ queryKey: ["get-all-applicants"] });
      queryClient.refetchQueries({ queryKey: ["get-shortlisted-applicants"] });
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to shortlist applicant");
      // Clear loading state on error
      setLoadingStates((prev) => {
        const newState = { ...prev };
        // Find the key that has "SHORTLISTING" value and clear it
        Object.keys(newState).forEach((key) => {
          if (newState[key] === "SHORTLISTING") {
            newState[key] = "";
          }
        });
        return newState;
      });
    },
  });

  const { mutate: rejectApplicant } = useRejectApplicant({
    onSuccess: (data) => {
      toast.success(data.message || "Applicant rejected successfully");
      setLoadingStates((prev) => ({
        ...prev,
        [data.data.application._id]: "",
      }));

      queryClient.refetchQueries({ queryKey: ["get-job-applicants"] });
      queryClient.refetchQueries({ queryKey: ["get-recruiter-jobs"] });
      queryClient.refetchQueries({ queryKey: ["get-all-applicants"] });
      queryClient.refetchQueries({ queryKey: ["get-shortlisted-applicants"] });
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to reject applicant");
      // Clear loading state on error
      setLoadingStates((prev) => {
        const newState = { ...prev };
        // Find the key that has "REJECTING" value and clear it
        Object.keys(newState).forEach((key) => {
          if (newState[key] === "REJECTING") {
            newState[key] = "";
          }
        });
        return newState;
      });
    },
  });

  const { mutate: deleteApplication } = useDeleteApplication({
    onSuccess: (data, variables) => {
      toast.success(data.message || "Application removed successfully");
      setLoadingStates((prev) => {
        const newState = { ...prev };
        delete newState[variables]; // Use the applicationId from variables
        return newState;
      });

      queryClient.refetchQueries({ queryKey: ["get-job-applicants"] });
      queryClient.refetchQueries({ queryKey: ["get-recruiter-jobs"] });
      queryClient.refetchQueries({ queryKey: ["get-all-applicants"] });
      queryClient.refetchQueries({ queryKey: ["get-shortlisted-applicants"] });
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to remove application");
      // Clear loading state on error
      setLoadingStates((prev) => {
        const newState = { ...prev };
        // Find the key that has "DELETING" value and clear it
        Object.keys(newState).forEach((key) => {
          if (newState[key] === "DELETING") {
            newState[key] = "";
          }
        });
        return newState;
      });
    },
  });

  // Create conversation mutation
  const { mutate: createConversation } = useCreateConversation({
    onSuccess: (data) => {
      toast.success(data.message || "Conversation created successfully");

      // The useCreateConversation hook will automatically join the conversation
      // via socket, which will trigger the join_conversation event

      // Navigate to the message page with the new conversation ID
      router.push(`/message?id=${data.data._id}`);

      // Clear loading state
      setLoadingStates((prev) => {
        const newState = { ...prev };
        // Find the key that has "MESSAGING" value and clear it
        Object.keys(newState).forEach((key) => {
          if (newState[key] === "MESSAGING") {
            newState[key] = "";
          }
        });
        return newState;
      });
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to create conversation");
      // Clear loading state on error
      setLoadingStates((prev) => {
        const newState = { ...prev };
        // Find the key that has "MESSAGING" value and clear it
        Object.keys(newState).forEach((key) => {
          if (newState[key] === "MESSAGING") {
            newState[key] = "";
          }
        });
        return newState;
      });
    },
  });

  // Handle shortlist action
  const handleShortlist = (applicationId: string) => {
    if (!applicationId) return;
    setLoadingStates((prev) => ({
      ...prev,
      [applicationId]: "SHORTLISTING",
    }));
    shortlistApplicant(applicationId);
  };

  // Handle reject action
  const handleReject = (applicationId: string) => {
    if (!applicationId) return;
    setLoadingStates((prev) => ({
      ...prev,
      [applicationId]: "REJECTING",
    }));
    rejectApplicant(applicationId);
  };

  // Handle delete action
  const handleDelete = (applicationId: string) => {
    if (!applicationId) return;
    setLoadingStates((prev) => ({
      ...prev,
      [applicationId]: "DELETING",
    }));
    deleteApplication(applicationId);
  };

  // Handle message action
  const handleMessage = (applicationId: string) => {
    if (!applicationId) return;
    setLoadingStates((prev) => ({
      ...prev,
      [applicationId]: "MESSAGING",
    }));
    createConversation({
      type: "application",
      jobApplicationId: applicationId,
    });
  };
  return (
    <Table className="border-t border-gray-300">
      <TableHeader>
        <TableRow>
          <TableHead className="text-orange-100 font-bold p-5">Name</TableHead>
          <TableHead className="text-orange-100 font-bold p-5">Job Title</TableHead>
          <TableHead className="text-orange-100 font-bold p-5">Applied Date</TableHead>
          <TableHead className="text-orange-100 font-bold p-5">Status</TableHead>
          <TableHead className="text-orange-100 font-bold p-5">Action</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {data?.map((row, index) => (
          <TableRow key={index}>
            <TableCell className="p-5">
              <div className="sm:flex">
                <Image
                  src={row?.imageUrl || DEFAULT_IMAGE}
                  alt=""
                  width={48}
                  height={38}
                  className="w-[48px] h-[48px] rounded-full object-cover"
                />
                <div className="sm:ml-3 mt-3 sm:mt-0">
                  <span className="text-base text-orange-100 font-medium underline leading-6 block">
                    {row.name}
                  </span>
                  <span className="text-base text-black-100 font-normal block">
                    {row.designation}
                  </span>
                </div>
              </div>
            </TableCell>
            <TableCell className="p-5">
              <span className="underline text-black-100 font-normal">{row.jobTitle}</span>
            </TableCell>
            <TableCell className="p-5">{row.appliedDate}</TableCell>
            <TableCell className="p-5">
              <span
                className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeColor(row.status)}`}
              >
                {row.status || "PENDING"}
              </span>
            </TableCell>
            <TableCell className="p-5 ">
              <div className="lg:hidden">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="border-0">
                      <Ellipsis className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    {!hideShortlistAction &&
                      (loadingStates[row.id || ""] === "SHORTLISTING" ? (
                        <DropdownMenuItem className="text-orange-100">
                          <LoadingSpinner size="small" />{" "}
                          <span className="ml-2">Shortlisting...</span>
                        </DropdownMenuItem>
                      ) : (
                        <DropdownMenuItem
                          className={`${row.status === "SHORTLISTED" ? "text-orange-100" : "text-gray-100"}`}
                          onClick={() => row.id && handleShortlist(row.id)}
                        >
                          <CheckeDouble /> <span className="ml-2">Shortlist</span>
                        </DropdownMenuItem>
                      ))}

                    {loadingStates[row.id || ""] === "REJECTING" ? (
                      <DropdownMenuItem className="text-red-600">
                        <LoadingSpinner size="small" /> <span className="ml-2">Rejecting...</span>
                      </DropdownMenuItem>
                    ) : (
                      <DropdownMenuItem
                        className={`${row.status === "REJECTED" ? "text-red-600" : "text-gray-100"}`}
                        onClick={() => row.id && handleReject(row.id)}
                      >
                        <Prohibitlnst /> <span className="ml-2">Reject</span>
                      </DropdownMenuItem>
                    )}

                    {row.cvUrl && (
                      <DropdownMenuItem className="text-blue-100" asChild>
                        <a href={row.cvUrl} target="_blank" rel="noopener noreferrer">
                          <FileArrowDown /> <span className="ml-2">Download CV</span>
                        </a>
                      </DropdownMenuItem>
                    )}

                    {loadingStates[row.id || ""] === "MESSAGING" ? (
                      <DropdownMenuItem className="text-orange-100">
                        <LoadingSpinner size="small" />{" "}
                        <span className="ml-2">Creating chat...</span>
                      </DropdownMenuItem>
                    ) : (
                      <DropdownMenuItem
                        className="text-orange-100"
                        onClick={() => row.id && handleMessage(row.id)}
                      >
                        <ChatDots /> <span className="ml-2">Message</span>
                      </DropdownMenuItem>
                    )}

                    {loadingStates[row.id || ""] === "DELETING" ? (
                      <DropdownMenuItem className="text-red-600">
                        <LoadingSpinner size="small" /> <span className="ml-2">Removing...</span>
                      </DropdownMenuItem>
                    ) : (
                      <DropdownMenuItem
                        className="text-red-600"
                        onClick={() => row.id && handleDelete(row.id)}
                      >
                        <CrossRounded /> <span className="ml-2">Remove</span>
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              <div className="hidden lg:flex flex-wrap gap-x-3">
                {!hideShortlistAction &&
                  (loadingStates[row.id || ""] === "SHORTLISTING" ? (
                    <div className="text-orange-100">
                      <LoadingSpinner size="small" />
                    </div>
                  ) : (
                    <div
                      className={`${row.status === "SHORTLISTED" ? "text-orange-100" : "text-gray-100"} cursor-pointer`}
                      title="Shortlist"
                      onClick={() => row.id && handleShortlist(row.id)}
                    >
                      <CheckeDouble />
                    </div>
                  ))}

                {loadingStates[row.id || ""] === "REJECTING" ? (
                  <div className="text-red-600">
                    <LoadingSpinner size="small" />
                  </div>
                ) : (
                  <div
                    className={`${row.status === "REJECTED" ? "text-red-600" : "text-gray-100"} cursor-pointer`}
                    title="Reject"
                    onClick={() => row.id && handleReject(row.id)}
                  >
                    <Prohibitlnst />
                  </div>
                )}

                {row.cvUrl && (
                  <a
                    href={row.cvUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-100 cursor-pointer"
                    title="Download CV"
                  >
                    <FileArrowDown />
                  </a>
                )}

                {loadingStates[row.id || ""] === "MESSAGING" ? (
                  <div className="text-orange-100">
                    <LoadingSpinner size="small" />
                  </div>
                ) : (
                  <div
                    onClick={() => row.id && handleMessage(row.id)}
                    className="text-orange-100 cursor-pointer"
                    title="Message"
                  >
                    <ChatDots />
                  </div>
                )}

                {loadingStates[row.id || ""] === "DELETING" ? (
                  <div className="text-red-600">
                    <LoadingSpinner size="small" />
                  </div>
                ) : (
                  <div
                    className="text-red-600 cursor-pointer"
                    title="Remove"
                    onClick={() => row.id && handleDelete(row.id)}
                  >
                    <CrossRounded />
                  </div>
                )}
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export default CompanyDashboardTable;
