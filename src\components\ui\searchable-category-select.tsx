"use client";

import { ChevronDown, Search, X } from "lucide-react";
import { useState, useEffect, useRef, useCallback } from "react";
import { useGetCategories } from "@/hooks/useQuery";
import type { ICategory } from "@/types/query.types";

interface SearchableCategorySelectProps {
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function SearchableCategorySelect({
  value,
  onValueChange,
  placeholder = "Select category",
  className = "",
  disabled = false,
}: SearchableCategorySelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(1);
  const [allCategories, setAllCategories] = useState<ICategory[]>([]);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const listRef = useRef<HTMLDivElement>(null);

  // Debounced search term
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Fetch categories with search and pagination
  const { data: categoriesData, isLoading } = useGetCategories(
    {
      search: debouncedSearchTerm || undefined,
      page: page,
      limit: 20,
    },
    {
      enabled: isOpen || !!debouncedSearchTerm,
    }
  );

  // Update categories list when data changes
  useEffect(() => {
    if (categoriesData?.data) {
      if (page === 1 || debouncedSearchTerm !== searchTerm) {
        // Reset list for new search or first page
        setAllCategories(categoriesData.data.categories);
      } else {
        // Append to existing list for pagination
        setAllCategories((prev) => [...prev, ...categoriesData.data.categories]);
      }
      setHasNextPage(categoriesData.data.pagination.hasNextPage);
      setIsLoadingMore(false);
    }
  }, [categoriesData, page, debouncedSearchTerm, searchTerm]);

  // Reset page when search term changes
  useEffect(() => {
    setPage(1);
    setAllCategories([]);
  }, [debouncedSearchTerm]);

  // Handle scroll for infinite loading
  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

      if (
        scrollHeight - scrollTop <= clientHeight + 10 &&
        hasNextPage &&
        !isLoading &&
        !isLoadingMore
      ) {
        setIsLoadingMore(true);
        setPage((prev) => prev + 1);
      }
    },
    [hasNextPage, isLoading, isLoadingMore]
  );

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm("");
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  const handleSelect = (category: ICategory) => {
    onValueChange(category.category);
    setIsOpen(false);
    setSearchTerm("");
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onValueChange("");
  };

  const selectedCategory = allCategories.find((cat) => cat.category === value);
  const displayValue = selectedCategory?.category || value;

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Trigger Button */}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`
          w-full h-[60px] px-4 rounded-full border border-gray-300 shadow
          flex items-center justify-between bg-white text-left
          ${disabled ? "opacity-50 cursor-not-allowed" : "hover:border-gray-400 cursor-pointer"}
          ${isOpen ? "border-gray-300 ring-1" : ""}
        `}
      >
        <span className={displayValue ? "text-gray-900" : "text-gray-500"}>
          {displayValue || placeholder}
        </span>
        <div className="flex items-center gap-2">
          {value && !disabled && (
            <X className="h-4 w-4 text-gray-400 hover:text-gray-600" onClick={handleClear} />
          )}
          <ChevronDown
            className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? "rotate-180" : ""}`}
          />
        </div>
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-2 bg-white border border-gray-300 rounded-lg shadow-lg max-h-80 overflow-hidden">
          {/* Search Input */}
          <div className="p-3 border-b border-gray-200">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-100" />
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search categories..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 text-gray-100 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
              />
            </div>
          </div>

          {/* Categories List */}
          <div ref={listRef} className="max-h-60 overflow-y-auto" onScroll={handleScroll}>
            {isLoading && page === 1 ? (
              <div className="p-4 text-center text-gray-500">Loading categories...</div>
            ) : allCategories.length > 0 ? (
              <>
                {allCategories.map((category) => (
                  <button
                    key={category._id}
                    type="button"
                    onClick={() => handleSelect(category)}
                    className={`
                      w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors
                      ${value === category.category ? "bg-orange-50 text-orange-600" : "text-gray-900"}
                    `}
                  >
                    {category.category}
                  </button>
                ))}
                {isLoadingMore && (
                  <div className="p-4 text-center text-gray-500">Loading more...</div>
                )}
              </>
            ) : (
              <div className="p-4 text-center text-gray-500">
                {debouncedSearchTerm ? "No categories found" : "No categories available"}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
