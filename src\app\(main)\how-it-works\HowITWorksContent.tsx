"use client";

import Image from "next/image";
import PrimaryButton from "@/components/Buttons/PrimaryButton";
import HowItInfoCard from "@/components/Cards/HowItInfoCard";
import HowItWorkStepCard from "@/components/Cards/HowItWorkStepCard";
import HowItWorksCard from "@/components/Cards/HowItWorksCard";
import { PrimaryHeading } from "@/components/Headings/PrimaryHeading";
// import { BellIcon, BuildingIcon, FollowerLutos, HeadsetIcon } from "@/components/Icons";
import CTASection from "@/components/Sections/CTASection";
import MobileAppSection from "@/components/Sections/MobileAppSection";
import { useGetHowItWorksPage } from "@/hooks/useQuery";
const HowITWorksContent = () => {
  const { data: howItWorksPageData, isLoading, isError } = useGetHowItWorksPage();
  const forCandidates = howItWorksPageData?.data.page.forCandidates;
  const forRecruiters = howItWorksPageData?.data.page.forRecruiters;
  const benefits = howItWorksPageData?.data.page.benefits;

  if (isLoading) {
    return (
      <div className="container mx-auto py-20 text-center">
        <div className="animate-pulse space-y-4">
          <div className="h-10 bg-gray-200 rounded w-1/2 mx-auto" />
          <div className="h-8 bg-gray-200 rounded w-2/3 mx-auto" />
          <div className="h-[400px] bg-gray-200 rounded-lg mt-10" />
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="container mx-auto py-20 text-center text-red-500">
        Failed to load About Us content. Please try again later.
      </div>
    );
  }

  return (
    <>
      <section className="py-20 relative">
        <div className="absolute w-full top-0 left-0 z-[-1]">
          <Image alt="" src={"/images/spiral.png"} width={837} height={1920} className="w-full" />
        </div>
        <div className="container mx-auto text-center">
          <PrimaryHeading>{howItWorksPageData?.data.page.heading}</PrimaryHeading>
          <p className="text-gray-100 py-6">{howItWorksPageData?.data.page.description}</p>
          <div className="flex justify-center gap-4">
            <PrimaryButton link="#" text="Find Job" />
            <PrimaryButton link="#" text="Find Talent" variant="secondary" />
          </div>
          <div className="mt-20">
            <Image
              src={howItWorksPageData?.data.page.image || "/images/how-it-works.png"}
              alt=""
              width={1520}
              height={738}
            />
          </div>
        </div>
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-4 md:grid-cols-2 grid-cols-1 gap-8 mt-20">
            {benefits?.map((benefit) => (
              <HowItWorksCard
                key={benefit._id}
                description={benefit.description}
                icon={benefit.iconImage}
                title={benefit.heading}
              />
            ))}
            {/* <HowItWorksCard
              description="Connect with forward-thinking companies that value talent and innovation."
              icon={<BuildingIcon />}
              title="Proactive Employers"
            />
            <HowItWorksCard
              description="Stay ahead with real-time alerts and seamless application tracking."
              icon={<BellIcon />}
              title="No Missed Opportunities"
            />
            <HowItWorksCard
              description="Foster a fair and inclusive hiring environment with objective matching algorithms."
              icon={<FollowerLutos />}
              title="Reduce Hiring Bias"
            />
            <HowItWorksCard
              description="Get assistance whenever you need it, with our dedicated round-the-clock team."
              icon={<HeadsetIcon />}
              title="24/7 Support"
            /> */}
          </div>
          <div className="grid lg:grid-cols-2 md:grid-col-1 grid-cols-1 gap-8 mt-20">
            <HowItInfoCard
              description={forCandidates?.description || ""}
              link="#"
              mainTitle={forCandidates?.heading || ""}
              smallTitle="Step-by-Step"
              buttonText="Find Job"
            />
            <div className="space-y-8">
              {forCandidates?.steps.map((step, index) => (
                <HowItWorkStepCard
                  key={step._id}
                  countTitle={index + 1}
                  text={step.description}
                  title={step.heading}
                />
              ))}
              {/* <HowItWorkStepCard
                countTitle="1"
                text="Create an account using email, social media, or phone number."
                title="Sign Up"
              />
              <HowItWorkStepCard
                countTitle="2"
                text="Add your resume, skills, and job preferences to tailor your experience."
                title="Build Your Profile"
              /> */}
            </div>
          </div>
        </div>
      </section>
      <CTASection />

      <section>
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 md:grid-col-1 grid-cols-1 gap-10 mt-20">
            <div className="space-y-8">
              {forRecruiters?.steps.map((step, index) => (
                <HowItWorkStepCard
                  key={step._id}
                  countTitle={index + 1}
                  text={step.description}
                  title={step.heading}
                />
              ))}
              {/* <HowItWorkStepCard
                countTitle="1"
                text="Create an account using email, social media, or phone number."
                title="Sign Up"
              />
              <HowItWorkStepCard
                countTitle="2"
                text="Add your resume, skills, and job preferences to tailor your experience."
                title="Build Your Profile"
              /> */}
            </div>
            <HowItInfoCard
              description={forRecruiters?.description || ""}
              link="#"
              mainTitle={forRecruiters?.heading || ""}
              smallTitle="Step-by-Step"
              buttonText="Find Talent"
            />
          </div>
        </div>
      </section>

      <MobileAppSection />
    </>
  );
};

export default HowITWorksContent;
