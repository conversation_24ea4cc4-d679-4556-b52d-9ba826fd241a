"use client";

import Image from "next/image";
import React from "react";
import PrimaryButton from "../Buttons/PrimaryButton";
import { PrimaryHeading } from "../Headings/PrimaryHeading";
import { ArrowIcon } from "../Icons";
import { useGetOpportunitySection, useGetTalentSection } from "@/hooks/useQuery";

const FeaturesSection = () => {
  const { data: opportunitySectionData } = useGetOpportunitySection();
  const { data: talentSectionData } = useGetTalentSection();
  return (
    <section className="lg:py-20 py-14">
      <div className="container mx-auto">
        <div className="lg:flex gap-10 items-center">
          <div className="lg:w-[60%] mb-4 lg:mb-0">
            {/* <PrimaryHeading>
              Your <span>Next Opportunity</span>{" "}
              <span className="text-black-100 lg:block"> Awaits </span>
            </PrimaryHeading> */}
            <PrimaryHeading>
              {(() => {
                const heading = opportunitySectionData?.data.section.heading || "";
                const words = heading.trim().split(" ");

                if (words.length < 3) return heading; // fallback for short titles

                const first = words[0];
                const middle = words.slice(1, -1).join(" ");
                const last = words[words.length - 1];

                return (
                  <>
                    {first} <span className="text-[#EC761E]">{middle}</span>{" "}
                    <span className="text-black-100 lg:block">{last}</span>
                  </>
                );
              })()}
            </PrimaryHeading>
            <p className="text-gray-100 my-8">
              {/* At YesJobs, we empower you to take control of your career journey by providing
              personalized job recommendations, real-time alerts, and tools to showcase your unique
              skills and experience to top employers. */}
              {opportunitySectionData?.data.section.description}
            </p>
            <div className="flex flex-wrap items-center gap-6">
              <div>
                <PrimaryButton link="/about-us" text="Learn More" icon={<ArrowIcon />} />
              </div>
              <div>
                <PrimaryButton link="/contact" text="Contact Us" variant="secondary" />
              </div>
            </div>
            <div className="flex lg:gap-6 gap-6 lg:flex-nowrap flex-wrap justify-between mt-16 ">
              {opportunitySectionData?.data.section.steps.map((item, index) => {
                return (
                  <div className="flex gap-x-4" key={item._id}>
                    <div>
                      <h3 className="text-orange-100 leading-none font-bold text-[40px]">
                        {/* 01 */}0{index + 1}
                      </h3>
                    </div>
                    <div>
                      <h4 className="text-black-100 font-bold text-[28px] mb-6">{item.heading}</h4>
                      <p className="text-gray-100 leading-[30px]">{item.description}</p>
                    </div>
                  </div>
                );
              })}
              {/* <div className="flex gap-x-4">
                <div>
                  <h3 className="text-orange-100 leading-none font-bold text-[40px]">02</h3>
                </div>
                <div>
                  <h4 className="text-black-100 font-bold text-[28px]  mb-6">
                    Application Tracking
                  </h4>
                  <p className="text-gray-100 leading-[30px]">
                    Manage and monitor your job applications in one place.
                  </p>
                </div>
              </div> */}
            </div>
          </div>
          <div className="lg:w-[40%]">
            <Image
              src={opportunitySectionData?.data.section.image || "/images/about-right-1.png"}
              alt="About Us Home"
              width={684}
              height={691}
            />
          </div>
        </div>
        <div className="lg:flex gap-16 pt-16 items-center">
          <div className="lg:w-[40%]">
            <Image
              src={talentSectionData?.data.section.image || "/images/about-left-1.png"}
              alt="About Us Home"
              width={684}
              height={691}
            />
          </div>
          <div className="lg:w-[60%] mt-6 lg:mt-0">
            <PrimaryHeading>
              {(() => {
                const heading = talentSectionData?.data.section.heading || "";
                const words = heading.trim().split(" ");

                if (words.length < 3) return heading; // fallback for short titles

                const first = words[0];
                const middle = words.slice(1, -1).join(" ");
                const last = words[words.length - 1];

                return (
                  <>
                    {first} <span className="text-[#EC761E]">{middle}</span>{" "}
                    <span className="text-black-100 lg:block">{last}</span>
                  </>
                );
              })()}
            </PrimaryHeading>
            <p className="text-gray-100 my-8">{talentSectionData?.data.section.description}</p>

            <div className="flex lg:gap-6 gap-6 lg:flex-nowrap flex-wrap justify-between mt-16">
              {talentSectionData?.data.section.steps.map((item) => {
                return (
                  <div className="flex gap-x-4" key={item._id}>
                    <div>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="38"
                        height="37"
                        fill="none"
                        viewBox="0 0 38 37"
                      >
                        <mask
                          id="mask0_23_1490"
                          width="38"
                          height="37"
                          x="0"
                          y="0"
                          maskUnits="userSpaceOnUse"
                          style={{ maskType: "alpha" }}
                        >
                          <path fill="#D9D9D9" d="M.781 0h36.285v36.571H.781z"></path>
                        </mask>
                        <g mask="url(#mask0_23_1490)">
                          <path
                            fill="#EC761E"
                            d="m13.436 36-3.109-5.486-5.89-1.371.572-6.343L1 18l4.01-4.8-.574-6.343 5.891-1.371L13.437 0 19 2.486 24.564 0l3.109 5.486 5.89 1.371-.572 6.343L37 18l-4.01 4.8.574 6.343-5.891 1.371L24.563 36 19 33.514zm1.391-4.371L19 29.743l4.255 1.886 2.29-4.115 4.5-1.114-.409-4.8 3.028-3.6-3.028-3.686.41-4.8-4.5-1.028-2.373-4.115L19 6.257l-4.254-1.886-2.292 4.115-4.5 1.028.41 4.8L5.336 18l3.028 3.6-.41 4.886 4.5 1.028zm2.455-7.543 9.245-9.686-2.29-2.486-6.955 7.286-3.518-3.6-2.291 2.4z"
                          ></path>
                        </g>
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-black-100 font-bold text-[28px] mb-6">{item.heading}</h4>
                      <p className="text-gray-100 leading-[30px]">{item.description}</p>
                    </div>
                  </div>
                );
              })}
              {/* <div className="flex gap-x-4">
                <div>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="38"
                    height="37"
                    fill="none"
                    viewBox="0 0 38 37"
                  >
                    <mask
                      id="mask0_23_1490"
                      width="38"
                      height="37"
                      x="0"
                      y="0"
                      maskUnits="userSpaceOnUse"
                      style={{ maskType: "alpha" }}
                    >
                      <path fill="#D9D9D9" d="M.781 0h36.285v36.571H.781z"></path>
                    </mask>
                    <g mask="url(#mask0_23_1490)">
                      <path
                        fill="#EC761E"
                        d="m13.436 36-3.109-5.486-5.89-1.371.572-6.343L1 18l4.01-4.8-.574-6.343 5.891-1.371L13.437 0 19 2.486 24.564 0l3.109 5.486 5.89 1.371-.572 6.343L37 18l-4.01 4.8.574 6.343-5.891 1.371L24.563 36 19 33.514zm1.391-4.371L19 29.743l4.255 1.886 2.29-4.115 4.5-1.114-.409-4.8 3.028-3.6-3.028-3.686.41-4.8-4.5-1.028-2.373-4.115L19 6.257l-4.254-1.886-2.292 4.115-4.5 1.028.41 4.8L5.336 18l3.028 3.6-.41 4.886 4.5 1.028zm2.455-7.543 9.245-9.686-2.29-2.486-6.955 7.286-3.518-3.6-2.291 2.4z"
                      ></path>
                    </g>
                  </svg>
                </div>
                <div>
                  <h4 className="text-black-100 font-bold text-[28px] mb-6">Create Job Listings</h4>
                  <p className="text-gray-100 leading-[30px]">
                    Craft detailed job ads to attract top talent.
                  </p>
                </div>
              </div>
              <div className="flex gap-x-4">
                <div>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="38"
                    height="37"
                    fill="none"
                    viewBox="0 0 38 37"
                  >
                    <mask
                      id="mask0_23_1490"
                      width="38"
                      height="37"
                      x="0"
                      y="0"
                      maskUnits="userSpaceOnUse"
                      style={{ maskType: "alpha" }}
                    >
                      <path fill="#D9D9D9" d="M.781 0h36.285v36.571H.781z"></path>
                    </mask>
                    <g mask="url(#mask0_23_1490)">
                      <path
                        fill="#EC761E"
                        d="m13.436 36-3.109-5.486-5.89-1.371.572-6.343L1 18l4.01-4.8-.574-6.343 5.891-1.371L13.437 0 19 2.486 24.564 0l3.109 5.486 5.89 1.371-.572 6.343L37 18l-4.01 4.8.574 6.343-5.891 1.371L24.563 36 19 33.514zm1.391-4.371L19 29.743l4.255 1.886 2.29-4.115 4.5-1.114-.409-4.8 3.028-3.6-3.028-3.686.41-4.8-4.5-1.028-2.373-4.115L19 6.257l-4.254-1.886-2.292 4.115-4.5 1.028.41 4.8L5.336 18l3.028 3.6-.41 4.886 4.5 1.028zm2.455-7.543 9.245-9.686-2.29-2.486-6.955 7.286-3.518-3.6-2.291 2.4z"
                      ></path>
                    </g>
                  </svg>
                </div>
                <div>
                  <h4 className="text-black-100 font-bold text-[28px]  mb-6">
                    Applicant Management
                  </h4>
                  <p className="text-gray-100 leading-[30px]">
                    Organize and communicate with candidates via your dashboard.
                  </p>
                </div>
              </div> */}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
